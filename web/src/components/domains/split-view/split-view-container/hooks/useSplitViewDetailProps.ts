import * as React from 'react';
import type { SplitViewDetailMessage } from '../../split-view-detail/SplitViewDetail';

/**
 * SplitViewDetail共通Props型定義
 */
export interface SplitViewDetailCommonProps {
  className: string;
  onSwipingBack: (step: number) => void;
  fetchDisplayNames: (userIds: string[]) => Promise<Map<string, string>>;
  groupByReactions: (
    reactions: { reactionType: string; createdDateTime: string; user: { user?: { id?: string } } }[]
  ) => Promise<Map<string, { displayName: string; userIds: string[] }>>;
  replyOptionsProp?: {
    onClickReply?: () => void;
    onClickReplyAll?: () => void;
    onClickForward?: () => void;
  };
  isTransactionPendingBookmarkRepos: boolean;
  onClickBookmarkOnDetail: (
    id: string,
    isBookmarked: boolean,
    title: string,
    url: string,
  ) => void;
}

/**
 * 検索結果詳細のProps型定義
 */
export interface SearchResultDetailProps extends SplitViewDetailCommonProps {
  open: boolean;
  view: string;
  message: SplitViewDetailMessage;
  detail: unknown;
  activeId: string;
  isBookmarked: boolean;
  onClickAttachment: (url: string) => void;
  onClose: () => void;
  onClickAltLink: (url: string) => void;
  onChangeDetail?: () => void;
  $bodyRef: React.RefObject<HTMLDivElement>;
}

/**
 * ブックマーク詳細のProps型定義
 */
export interface BookmarkDetailProps extends SplitViewDetailCommonProps {
  open: boolean;
  view: string;
  message: typeSplitViewDetailMessage;
  detail: unknown;
  activeId: string;
  isBookmarked: boolean;
  onClickAttachment: (url: string) => void;
  onClose: () => void;
  onClickAltLink: (url: string) => void;
  $bodyRef: React.RefObject<HTMLDivElement>;
}

/**
 * SplitViewDetailの共通Propsを生成するフック
 *
 * @param swipeBackEffectClass - スワイプバックエフェクトのクラス名
 * @param setSwipeBackEffectStep - スワイプバックエフェクトのステップ設定関数
 * @param fetchDisplayNames - 表示名取得関数
 * @param groupUsersByReaction - リアクション別ユーザーグループ化関数
 * @param replyOptions - 返信オプション
 * @param isTransactionPendingBookmarkRepos - ブックマークトランザクション中フラグ
 * @param onClickBookmarkOnDetail - 詳細画面でのブックマーククリックハンドラ
 * @returns 共通Props
 */
export default function useSplitViewDetailProps(
  swipeBackEffectClass: string,
  setSwipeBackEffectStep: (step: number) => void,
  fetchDisplayNames: (userIds: string[]) => Promise<Map<string, string>>,
  groupUsersByReaction: (
    reactions: { reactionType: string; createdDateTime: string; user: { user?: { id?: string } } }[]
  ) => Promise<Map<string, { displayName: string; userIds: string[] }>>,
  replyOptions: {
    onClickReply?: () => void;
    onClickReplyAll?: () => void;
    onClickForward?: () => void;
  } | undefined,
  isTransactionPendingBookmarkRepos: boolean,
  onClickBookmarkOnDetail: (
    id: string,
    isBookmarked: boolean,
    title: string,
    url: string,
  ) => void,
): SplitViewDetailCommonProps {
  return React.useMemo(
    () => ({
      className: swipeBackEffectClass,
      onSwipingBack: setSwipeBackEffectStep,
      fetchDisplayNames,
      groupByReactions: groupUsersByReaction,
      replyOptionsProp: replyOptions,
      isTransactionPendingBookmarkRepos,
      onClickBookmarkOnDetail,
    }),
    [
      swipeBackEffectClass,
      setSwipeBackEffectStep,
      fetchDisplayNames,
      groupUsersByReaction,
      replyOptions,
      isTransactionPendingBookmarkRepos,
      onClickBookmarkOnDetail,
    ],
  );
}
