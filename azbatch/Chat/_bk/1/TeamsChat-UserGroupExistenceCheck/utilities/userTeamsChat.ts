import * as dotenv from 'dotenv';

dotenv.config();

const TEAMSCHAT_SEARCH_SIZE: string = process.env['TEAMSCHAT_SEARCH_SIZE'] ?? '20';
const TEAMSCHAT_START_DATE: string = process.env['TEAMSCHAT_START_DATE'] ?? '2025-01-31';
const TEAMSCHAT_END_DATE: string = process.env['TEAMSCHAT_END_DATE'] ?? '2025-12-31';
const TEAMSCHAT_MESSAGES_SEARCH_SIZE: string = process.env['TEAMSCHAT_MESSAGES_SEARCH_SIZE'] ?? '50';

interface UserMessageRequest {
  id: string;
  method: string;
  url: string;
}

function createCalendarWeekChunks(startDate: Date, endDate: Date): Array<{ start: string; end: string }> {
  const chunks = [];
  let currentDate = new Date(startDate);
  
  // Determine the day of week for the start date (0 = Sunday, 1 = Monday, etc.)
  const dayOfWeek = currentDate.getDay();
  
  if (dayOfWeek !== 0) {
    // Create a partial week from start date to the next Saturday
    const daysUntilSaturday = 6 - dayOfWeek; // Days until Saturday (6 - dayOfWeek)
    const nextSaturday = new Date(currentDate);
    nextSaturday.setDate(currentDate.getDate() + daysUntilSaturday);
    
    // If the first Saturday is beyond our end date, adjust accordingly
    const firstChunkEnd = nextSaturday > endDate ? endDate : nextSaturday;
    
    chunks.push({
      start: currentDate.toISOString().split('T')[0],
      end: firstChunkEnd.toISOString().split('T')[0]
    });
    
    // Set current date to the next Sunday
    currentDate = new Date(nextSaturday);
    currentDate.setDate(currentDate.getDate() + 1);
  }
  
  // Create full week chunks (Sunday to Saturday)
  while (currentDate < endDate) {
    // End date is Saturday (6 days after Sunday)
    const endOfWeek = new Date(currentDate);
    endOfWeek.setDate(currentDate.getDate() + 6);
    
    // If end of week exceeds the overall end date, use the end date
    const chunkEndDate = endOfWeek > endDate ? endDate : endOfWeek;
    
    chunks.push({
      start: currentDate.toISOString().split('T')[0],
      end: chunkEndDate.toISOString().split('T')[0]
    });
    
    // Move to next Sunday
    currentDate = new Date(endOfWeek);
    currentDate.setDate(currentDate.getDate() + 1);
  }
  
  return chunks;
}

export function createUserTeamsChatRequests(userId: string): UserMessageRequest[] {
  if (!userId) return [];

  const startDate = new Date(TEAMSCHAT_START_DATE);
  const endDate = new Date(TEAMSCHAT_END_DATE);
  const requests: UserMessageRequest[] = [];

  const dateChunks = createCalendarWeekChunks(startDate, endDate);
  
  dateChunks.forEach((chunk, index) => {
    requests.push({
      id: `${userId}_week${index + 1}`,
      method: 'GET',
      url: `/users/${userId}/chats?$top=${TEAMSCHAT_SEARCH_SIZE}&$filter=lastUpdatedDateTime ge ${chunk.start}T00:00:00Z and lastUpdatedDateTime le ${chunk.end}T23:59:59Z and chatType eq 'group'`
    });
  });
  
  return requests;

  // return [{
  //   id: userId,
  //   method: 'GET',
  //   url: `/users/${userId}/chats?$top=${TEAMSCHAT_SEARCH_SIZE}&$filter=lastUpdatedDateTime ge ${TEAMSCHAT_START_DATE}T00:00:00Z and lastUpdatedDateTime le ${TEAMSCHAT_END_DATE}T23:59:59Z and chatType eq 'group'`
  // }];
}

export function createUserTeamsChatMessagesRequests(userId: string, chatId: string): UserMessageRequest[] {
  if (!userId) return [];

  const userId_id = userId.split('_week')[0] ?? '';

  const startDate = new Date(TEAMSCHAT_START_DATE);
  const endDate = new Date(TEAMSCHAT_END_DATE);
  const requests: UserMessageRequest[] = [];

  const dateChunks = createCalendarWeekChunks(startDate, endDate);
  
  dateChunks.forEach((chunk, index) => {
    requests.push({
      id: `${userId}_${chatId}_chat${index + 1}`,
      method: 'GET',
      url: `/users/${userId_id}/chats/${chatId}/messages?$top=${TEAMSCHAT_MESSAGES_SEARCH_SIZE}&$filter=lastModifiedDateTime gt ${chunk.start}T00:00:00Z and lastModifiedDateTime lt ${chunk.end}T23:59:59Z`
    });
  });
  
  return requests;

  // return [{
  //   id: `${userId}_chat_${chatId}`,
  //   method: 'GET',
  //   url: `/users/${userId_id}/chats/${chatId}/messages?$top=${TEAMSCHAT_MESSAGES_SEARCH_SIZE}&$filter=lastModifiedDateTime gt ${TEAMSCHAT_START_DATE}T00:00:00Z and lastModifiedDateTime lt ${TEAMSCHAT_END_DATE}T23:59:59Z`
  // }];
}
