# UploadUserChat1on1Job

The `UploadUserChat1on1Job` is a batch process for inserting 1-on-1 chat messages into the cosmos DB. It ensures that messages exchanged between two users are properly handled, with the same message ID being used for both participants.

## Chat Filtering

Chats are filtered directly as 1-on-1 conversations using the condition:  
`chatType eq 'oneOnOne'` in the `userTeamsChat.ts`.

## Insertion Logic

The insertion logic, implemented in `cosmos.ts`, processes each message in the input array as follows:

1. **Check Message Existence**:  
   - If the message ID does not exist in the database, a new message is created.  
   - If the message ID already exists, the `security_user_id` field is checked.

2. **For Existing Messages**:  
   - Retrieve the current array of user IDs from the `security_user_id` field.  
   - Extract the new user IDs from the incoming message.  
   - Determine which user IDs need to be added (i.e., those present in the new message but not in the existing record).  
   - If there are new IDs to add:
     - Combine them with the existing IDs.
     - Update the message in the database.  
   - If no new IDs need to be added, the update is skipped.

---

# UploadUserChat1on1Job

`UploadUserChat1on1Job`は、1対1のチャットメッセージをCosmos DBに挿入するためのバッチプロセスです。このプロセスは、2人のユーザー間で交換されたメッセージが適切に処理され、両方の参加者に同じメッセージIDが使用されることを保証します。

## チャットのフィルタリング

チャットは、`userTeamsChat.ts`内で条件 `chatType eq 'oneOnOne'` を使用して、1対1の会話として直接フィルタリングされます。

## 挿入ロジック

`cosmos.ts`に実装された挿入ロジックは、入力配列内の各メッセージを以下の手順で処理します：

1. **メッセージの存在確認**:  
   - メッセージIDがデータベースに存在しない場合、新しいメッセージが作成されます。  
   - メッセージIDが既に存在する場合、`security_user_id`フィールドが確認されます。

2. **既存のメッセージの場合**:  
   - `security_user_id`フィールドから現在のユーザーID配列を取得します。  
   - 受信メッセージから新しいユーザーIDを抽出します。  
   - 追加が必要なユーザーIDを特定します（新しいメッセージに存在し、既存のレコードにないID）。  
   - 追加が必要なIDがある場合：  
     - 既存のIDと組み合わせます。  
     - データベース内のメッセージを更新します。  
   - 追加が必要なIDがない場合、更新はスキップされます。