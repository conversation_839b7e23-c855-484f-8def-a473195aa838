import { Entity, NullableOption, PublicError } from '@microsoft/microsoft-graph-types/microsoft-graph';

export type ValueOf<T> = T[keyof T];

/**
 * Graph API Batchリクエストのレスポンス1件分
 */
export interface IBatchResponseData {
  id?: string,
  status?: number,
  headers?: NullableOption<{ 'Retry-After'?: string, 'Content-Type'?: string }>
  body?: {
    value?: Entity[],
    error?: NullableOption<PublicError>,
  },
}

/**
 * Graph API Batchリクエストのレスポンス
 * Ref: BatchResponseBody
 */
export interface IBatchResponses {
  "@odata.nextLink"?: string;
  responses?: IBatchResponseData[],
}

/**
 * Teamsのアクティビティフィード設定
 */
export interface ITeamsActivityNotificationConfig {
  chainId?: number,
  topic?: {
    source?: string;
    value?: string,
  },
  activityType?: string;
  previewText?: {
    content?: string;
    contentType?: string;
  },
  templateParameters?: { [key: string]: string }[],
}

/**
 * Azure Functionsが渡すタイマーオブジェクト
 * https://docs.microsoft.com/ja-jp/azure/azure-functions/functions-bindings-timer?tabs=javascript
 */
export interface TimerObject {
  schedule: unknown;
  scheduleStatus: {
    last?: string;
    lastUpdated?: string;
    next?: string;
  },
  isPastDate: boolean;
}

/**
 * Teams Chats
 */
export interface ITeamsChats {
  id: string;
  topic: string;
  chatType: string;
}
/**
 * Teams Unique Chats
 */
export interface ITeamsUniqueChats {
  chatId: string;
  topic: string;
  chatType: string;
}
/**
 * Teams Chat Message Event
 */
export interface ITeamsChatMessageEvent {
  id: string;
  chatId: string;
  lastModifiedDateTime: string;
  body: {
    content: string;
  };
  eventDetail: IEventDetail | null;
}
export interface IEventDetail {
  "@odata.type": string;
  visibleHistoryStartDateTime: string;
  members: IEventMember[];
}
/**
 * Teams Chat Message Event
 */
export interface IEventMember {
  id: string;
  displayName: string | null;
  userIdentityType: string;
  tenantId: string;
}