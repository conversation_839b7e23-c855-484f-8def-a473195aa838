import { Con<PERSON><PERSON>, CosmosClient } from "@azure/cosmos";
import { IBatchResponseData, ITeamsChatMessageEvent } from "./models";
import { CustomLogger } from "./log";
import * as dotenv from "dotenv";

dotenv.config();

const cosmosDBEndpoint = process.env["COSMOS_DB_ENDPOINT"];
const cosmosDBKey = process.env["COSMOS_DB_KEY"];
const databaseName = process.env["COSMOS_DB_DATABASE"];
const containerName = process.env["COSMOS_DB_CONTAINER"];

let cosmosClient: CosmosClient | null = null;

function getClient(logger: CustomLogger) {
  try {
    if (!cosmosDBEndpoint || !cosmosDBKey) {
      throw new Error("[CosmosDB:getClient] cosmosDBEndpoint and cosmosDBKey must be defined");
    }
    if (!cosmosClient) {
      logger.log("[CosmosDB:getClient] Initializing Client Connection...");
      cosmosClient = new CosmosClient({
        endpoint: cosmosDBEndpoint,
        key: cosmosDBKey,
      });
      logger.log("[CosmosDB:getClient] Client Initialized Successfully");
    } else {
      logger.log("[CosmosDB:getClient] Reusing Existing Connection");
    }

    return cosmosClient;
  } catch (error) {
    logger.log(`[CosmosDB:getClient] Error Initialization: ${error}`);
    throw error;
  }
}

export async function validateCosmosDBConnection(
  logger: CustomLogger
): Promise<void> {
  try {
    if (!databaseName || !containerName) {
      throw new Error("[CosmosDB:validateCosmosDBConnection] databaseName and containerName must be defined");
    }
    const client = getClient(logger);

    // Test General Connection
    logger.log(`[CosmosDB:validateCosmosDBConnection] Testing Cosmos DB Connection...`);
    const { resources: databases } = await client.databases
      .readAll()
      .fetchAll();
    logger.log(`[CosmosDB:validateCosmosDBConnection] Successfully Connected! Found ${databases.length} Databases`);

    // Test Database Connection
    const database = client.database(databaseName);
    const dbResponse = await database.read();
    if (dbResponse.resource) {
      logger.log(`[CosmosDB:validateCosmosDBConnection] Connected to Database: ${dbResponse.resource.id}`);
    } else {
      throw new Error("[CosmosDB:validateCosmosDBConnection] Database resource is undefined");
    }

    // Test Container Connections
    const container = database.container(containerName);
    const containerResponse = await container.read();
    if (containerResponse.resource) {
      logger.log(`[CosmosDB:validateCosmosDBConnection] Connected to Container: ${containerResponse.resource.id}`);
    } else {
      throw new Error("[CosmosDB:validateCosmosDBConnection] Container resource is undefined");
    }

    // Count Container Details
    const {
      resources: [count],
    } = await container.items.query("SELECT VALUE COUNT(1) FROM c").fetchAll();
    logger.log(`[CosmosDB:validateCosmosDBConnection] Container has: ${count} Items`);
  } catch (error) {
    logger.log(`[CosmosDB:validateCosmosDBConnection] Error Connection: ${error}`);
    throw error;
  }
}

export async function checkTeams1on1GroupChatsMessages(
  logger: CustomLogger,
  dataTeams1on1GroupChatsMessages: IBatchResponseData[]
): Promise<void> {

  // logger.log(`[CosmosDB:checkTeams1on1GroupChatsMessages] dataTeams1on1GroupChatsMessages: ${JSON.stringify(dataTeams1on1GroupChatsMessages)}`); // !!!

  try {
    if (!databaseName || !containerName) {
      throw new Error("[CosmosDB:checkTeams1on1GroupChatsMessages] databaseName and containerName must be defined");
    }

    const client = getClient(logger);
    const container = client.database(databaseName).container(containerName);

    // Extract the messages with deleted members
    const modifiedDataTeams1on1GroupChatsMessages: ITeamsChatMessageEvent[][] = dataTeams1on1GroupChatsMessages
      .map((message) => message.body?.value || [])
      .filter(Array.isArray);
    
    const { removedCount, addedCount } = await processSystemMessages(container, modifiedDataTeams1on1GroupChatsMessages, logger);

    logger.log(`[CosmosDB:checkTeams1on1GroupChatsMessages] Removed ${removedCount} user IDs from security_user_id fields`);
    logger.log(`[CosmosDB:checkTeams1on1GroupChatsMessages] Added ${addedCount} user IDs to security_user_id fields`);

  } catch (error) {
    logger.log(`[CosmosDB:checkTeams1on1GroupChatsMessages] Error processing system messages: ${error}`);
    throw error;
  }
}

async function processSystemMessages(
  container: Container,
  modifiedDataTeams1on1GroupChatsMessages: ITeamsChatMessageEvent[][],
  logger: CustomLogger
): Promise<{ removedCount: number, addedCount: number }> {
  let removedCount = 0;
  let addedCount = 0;

  logger.log(`[CosmosDB:processSystemMessages] Total modifiedDataTeams1on1GroupChatsMessages: ${modifiedDataTeams1on1GroupChatsMessages.length}`);
  // logger.log(`[CosmosDB:processTeams1on1GroupChatsMessages] modifiedDataTeams1on1GroupChatsMessages: ${JSON.stringify(modifiedDataTeams1on1GroupChatsMessages)}`); // !!!

  for (const messageArray of modifiedDataTeams1on1GroupChatsMessages) {
    for (const message of messageArray) {
      if (!message.id || !message.eventDetail) {
        logger.log(`[CosmosDB:processSystemMessages] Skipping message with no ID or event details: ${JSON.stringify(message.id)}`);
        continue;
      }

      const eventType = message.eventDetail["@odata.type"];

      if (eventType === "#microsoft.graph.membersDeletedEventMessageDetail") {
        // Handle deletion events
        const removed = await processDeletedMembers(container, message, logger);
        removedCount += removed;
      } 
      else if (eventType === "#microsoft.graph.membersAddedEventMessageDetail") {
        // Handle addition events
        const added = await processAddedMembers(container, message, logger);
        addedCount += added;
      }
      else {
        logger.log(`[CosmosDB:processSystemMessages] Skipping message with unknown event type: ${eventType}`);
        continue;
      }
    }
  }
  
  return { removedCount, addedCount };
}

async function processDeletedMembers(
  container: Container, 
  message: ITeamsChatMessageEvent, 
  logger: CustomLogger
): Promise<number> {
  let removed = 0;
  // Extract the IDs of deleted users
  const deletedUserIds = message.eventDetail?.members?.map(member => member.id) || [];
  
  logger.log(`[CosmosDB:processDeletedMembers] Processing chatId ${message.chatId} with deleted members: ${JSON.stringify(deletedUserIds)}`);
  
  if (deletedUserIds.length === 0) {
    logger.log(`[CosmosDB:processDeletedMembers] No deleted user IDs found in message: ${message.id}`);
    return removed;
  }

  const basicQuerySpec = {
    query: "SELECT * FROM c WHERE c.chatId = @chatId",
    parameters: [{ name: "@chatId", value: message.chatId }],
  };

  try {
    const { resources: matchingMessages } = await container.items
      .query(basicQuerySpec)
      .fetchAll();

    if (matchingMessages.length === 0) {
      logger.log(`[CosmosDB:processDeletedMembers] No messages found with chatId: ${message.chatId}`);
      return removed;
    }
    
    logger.log(`[CosmosDB:processDeletedMembers] Found ${matchingMessages.length} messages with chatId: ${message.chatId}`);
    
    // Process each matching message
    for (const existingMsg of matchingMessages) {
      const existingUserIds = existingMsg.security_user_id || [];
      
      // Find which user IDs will be removed
      const userIdsToRemove = existingUserIds.filter((id: string) => deletedUserIds.includes(id));
      
      if (userIdsToRemove.length === 0) {
        logger.log(`[CosmosDB:processDeletedMembers] No matching user IDs to remove from message: ${existingMsg.id}`);
        continue;
      }
      
      // Filter out the deleted user IDs from the security_user_id array
      const updatedUserIds = existingUserIds.filter((id: string) => !deletedUserIds.includes(id));

      // Only update if there's a change in the array AND if the user is still present
      // This prevents redundant operations for already processed messages
      if (updatedUserIds.length < existingUserIds.length) {
        await container.item(existingMsg.id, existingMsg.id).replace({
          ...existingMsg,
          security_user_id: updatedUserIds,
        });
        
        const removedUsers = existingUserIds.length - updatedUserIds.length;
        removed += removedUsers;
        
        logger.log(`[CosmosDB:processDeletedMembers] Message ${existingMsg.id}: Removed ${removedUsers} user IDs: ${JSON.stringify(userIdsToRemove)}`);
      } else {
        logger.log(`[CosmosDB:processDeletedMembers] No user IDs removed from message: ${existingMsg.id}`);
      }
    }
  } catch (error) {
    logger.log(`[CosmosDB:processDeletedMembers] Error processing deleted users for chatId ${message.chatId}: ${error}`);
  }
  
  return removed;
}

// Function to handle member addition events
async function processAddedMembers(
  container: Container, 
  message: ITeamsChatMessageEvent, 
  logger: CustomLogger
): Promise<number> {
  let added = 0;
  // Extract the IDs of added users
  const addedUserIds = message.eventDetail?.members?.map(member => member.id) || [];
  const visibleHistoryStartDateTime = message.eventDetail?.visibleHistoryStartDateTime || null;
  
  logger.log(`[CosmosDB:processAddedMembers] Processing chatId: ${message.chatId} with added members: ${JSON.stringify(addedUserIds)}`);
  
  if (addedUserIds.length === 0) {
    logger.log(`[CosmosDB:processAddedMembers] No added user IDs found in message: ${message.id}`);
    return added;
  }

  const includeAllHistory = visibleHistoryStartDateTime === "0001-01-01T00:00:00Z" || visibleHistoryStartDateTime === "0001-01-01T00:00:00.000Z";

  try {
    if (includeAllHistory) {

      // If including all history, add users to all messages in the chat
      const allMessagesQuery = {
        query: "SELECT * FROM c WHERE c.chatId = @chatId",
        parameters: [{ name: "@chatId", value: message.chatId }],
      };
      
      const { resources: allMessages } = await container.items
        .query(allMessagesQuery)
        .fetchAll();
        
      logger.log(`[CosmosDB:processAddedMembers] Including All History: Found ${allMessages.length} Messages for chatId: ${message.chatId}`);
      
      // Update each message to include the added users
      for (const existingMsg of allMessages) {
        const existingUserIds = existingMsg.security_user_id || [];
        
        // Only add users that aren't already in the security_user_id array
        const newUserIds = addedUserIds.filter(id => !existingUserIds.includes(id));
        
        // Skip if all user IDs are already present (already processed by another user's event)
        if (newUserIds.length > 0) {
          const updatedUserIds = [...existingUserIds, ...newUserIds];
          
          await container.item(existingMsg.id, existingMsg.id).replace({
            ...existingMsg,
            security_user_id: updatedUserIds,
          });
          
          added += newUserIds.length;
          
          logger.log(`[CosmosDB:processAddedMembers] Message ${existingMsg.id}: Added ${newUserIds.length} user IDs: ${JSON.stringify(newUserIds)}`);
        } else {
          logger.log(`[CosmosDB:processAddedMembers] Message ${existingMsg.id}: All users already present, skipping update`);
        }
      }
    } else {
      // If not including all history, only add users to messages after visibleHistoryStartDateTime
      const newMessagesQuery = {
        query: "SELECT * FROM c WHERE c.chatId = @chatId AND c.lastModifiedDateTime >= @visibleFrom",
        parameters: [
          { name: "@chatId", value: message.chatId },
          { name: "@visibleFrom", value: visibleHistoryStartDateTime }
        ],
      };
      
      const { resources: newMessages } = await container.items
        .query(newMessagesQuery)
        .fetchAll();
        
      logger.log(`[CosmosDB:processAddedMembers] Limited History: Found ${newMessages.length} Mssages after ${visibleHistoryStartDateTime}`);
      
      // Update only the newer messages
      for (const existingMsg of newMessages) {
        const existingUserIds = existingMsg.security_user_id || [];
        
        // Only add users that aren't already in the security_user_id array
        const newUserIds = addedUserIds.filter(id => !existingUserIds.includes(id));
        
        // Skip if all user IDs are already present (already processed by another user's event)
        if (newUserIds.length > 0) {
          const updatedUserIds = [...existingUserIds, ...newUserIds];
          
          await container.item(existingMsg.id, existingMsg.id).replace({
            ...existingMsg,
            security_user_id: updatedUserIds,
          });
          
          added += newUserIds.length;
          
          logger.log(`[CosmosDB:processAddedMembers] Message ${existingMsg.id}: Added ${newUserIds.length} user IDs: ${JSON.stringify(newUserIds)}`);
        } else {
          logger.log(`[CosmosDB:processAddedMembers] Message ${existingMsg.id}: All users already present, skipping update`);
        }
      }
    }
  } catch (error) {
    logger.log(`[CosmosDB:processAddedMembers] Error processing added users for chatId ${message.chatId}: ${error}`);
  }
  
  return added;
}
