import * as dotenv from 'dotenv';

dotenv.config();

const TEAMS_1on1GROUP_CHATS_SEARCH_SIZE: string = process.env['TEAMS_1on1GROUP_CHATS_SEARCH_SIZE'] ?? '50';
const TEAMS_1on1GROUP_CHATS_START_DATE: string = process.env['TEAMS_1on1GROUP_CHATS_START_DATE'] ?? '2025-01-31';
const TEAMS_1on1GROUP_CHATS_END_DATE: string = process.env['TEAMS_1on1GROUP_CHATS_END_DATE'] ?? '2025-12-31';
const TEAMS_1on1GROUP_CHATS_MESSAGES_SEARCH_SIZE: string = process.env['TEAMS_1on1GROUP_CHATS_MESSAGES_SEARCH_SIZE'] ?? '50';

interface TeamsChannelRequest {
  id: string;
  method: string;
  url: string;
}

export function createTeams1on1GroupChatsRequests(userId: string): TeamsChannelRequest[] {
  if (!userId) return [];

  return [{
    id: userId,
    method: 'GET',
    url: `/users/${userId}/chats?$top=${TEAMS_1on1GROUP_CHATS_SEARCH_SIZE}&$filter=lastUpdatedDateTime ge ${TEAMS_1on1GROUP_CHATS_START_DATE}T00:00:00Z and lastUpdatedDateTime le ${TEAMS_1on1GROUP_CHATS_END_DATE}T23:59:59Z`
  }];
}

function formatDate(date: Date): string {
  return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;
}

function createYesterdayTodayChunks(): Array<{ start: string; end: string }> {
  const today = new Date();
  const yesterday = new Date(today);
  yesterday.setDate(today.getDate() - 1);

  const yesterdayStart = `${formatDate(yesterday)}T00:00:00Z`;
  const todayEnd = `${formatDate(today)}T23:59:59Z`;

  return [{
    start: yesterdayStart,
    end: todayEnd
  }];
}

export function createTeams1on1GroupChatsMessagesRequests(chatId: string): TeamsChannelRequest[] {
  if (!chatId) return [];

  const dateChunks = createYesterdayTodayChunks();

  return [{
    id: `${chatId}`,
    method: 'GET',
    url: `/chats/${chatId}/messages?$top=${TEAMS_1on1GROUP_CHATS_MESSAGES_SEARCH_SIZE}&$filter=lastModifiedDateTime gt ${dateChunks[0].start} and lastModifiedDateTime lt ${dateChunks[0].end}`
  }];
}

export function createChatsMembersRequests(chatId: string): TeamsChannelRequest[] {
  if (!chatId) return [];

  return [{
    id: chatId,
    method: 'GET',
    url: `/chats/${chatId}/members`
  }];
}