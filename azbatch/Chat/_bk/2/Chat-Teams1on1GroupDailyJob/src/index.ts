import { createGraphClient } from "../utilities/graph";
import { logger, logLongArray } from "../utilities/log";
import {
  createCredential,
  fetchTargetUsers,
  processTeams1on1GroupChats,
  processTeams1on1GroupChatsMessages,
  processTeams1on1GroupChatsMembers
} from "./impl";
import * as dotenv from "dotenv";
import { validateCosmosDBConnection } from "../utilities/cosmos";

dotenv.config();

async function main() {
  logger.log("========== PHASE 1 ==========");
  const credential = createCredential(
    process.env["AzureTenantId"] ?? "",
    process.env["AzureClientId"] ?? "",
    process.env["AzureClientSecret"] ?? ""
  );
  if (!credential) {
    logger.error("[Index:createCredential] NO_CREDENTIALS");
    return Promise.reject(new Error("[Index:createCredential] NO_CREDENTIALS"));
  }
  const graphClient = createGraphClient(credential);

  logger.log("\n========== PHASE 2 ==========");

  const teamsAppId = process.env["TeamsAppId"] ?? "";
  if (!teamsAppId) {
    logger.error("[Index:teamsAppId] NO_TEAMS_APP_ID");
    return Promise.reject(new Error("[Index:teamsAppId] NO_TEAMS_APP_ID"));
  }

  const groupId = process.env["GraphGroupId"] ?? "";
  if (!groupId) {
    logger.error("NO_GRAPH_GROUP_ID");
    return Promise.reject(new Error("[Index:groupId] NO_GRAPH_GROUP_ID"));
  }
  const targetUsers = await fetchTargetUsers(logger, graphClient, groupId, teamsAppId)
  .catch((reason) => {
    logger.error(reason);
    return Promise.reject(new Error("[Index:fetchTargetUsers] Error:", reason));
  });
  if (targetUsers.length === 0) {
    logger.log(
      "[Index:fetchTargetUsers] PROCESS_END_BECAUSE_OF_NO_USERS_TO_NOTIFY"
    );
    return Promise.resolve();
  }
  logLongArray(logger, "[Index:fetchTargetUsers] Target Users:", 800, targetUsers);

  logger.log("\n========== PHASE 3 ==========");
  logger.log("--- Validate CosmosDB Connection ---");
  try {
    await validateCosmosDBConnection(logger);
    logger.log("[Index:validateCosmosDBConnection] Successfully Connected to CosmosDB.");
  } catch (error) {
    logger.log(`[Index:validateCosmosDBConnection] CosmosDB Connection Failed: ${error}`);
    throw error;
  }

  logger.log("\n========== PHASE 4 ==========");
  logger.log("--- Processing Chats ---");
  const uniqueChats = await processTeams1on1GroupChats(logger, graphClient, targetUsers)
  .catch((reason) => {
    logger.error(reason);
    return Promise.reject(new Error("[Index:processTeams1on1GroupChats] Error:", reason));
  });
  logLongArray(logger, "[Index:processTeams1on1GroupChats] Unique Chats: ", 800, uniqueChats);
  logger.log(`[Index:processTeams1on1GroupChats] Unique Chats Count: ${uniqueChats.length}`);

  logger.log("\n========== PHASE 5 ==========");
  logger.log("--- Processing ChatsMessages ---");
  await processTeams1on1GroupChatsMessages(logger, graphClient, uniqueChats)
  .catch((reason) => {
    logger.error(reason);
    return Promise.reject(new Error("[Index:processTeams1on1GroupChatsMessages] Error:", reason));
  });
  logger.log(`[Index:processTeams1on1GroupChatsMessages] Inserting ChatsMessages - Completed`);

  logger.log("\n========== PHASE 6 ==========");
  logger.log("--- Processing ChatsMembers ---");
  await processTeams1on1GroupChatsMembers(logger, graphClient, uniqueChats, targetUsers)
  .catch((reason) => {
    logger.error(reason);
    return Promise.reject(new Error("[Index:processTeams1on1GroupChatsMembers] Error:", reason));
  });
  logger.log(`[Index:processTeams1on1GroupChatsMembers] Updating GroupChatsMembers - Completed`);

  logger.log("\n========== PHASE 7 ==========");
  logger.log("[Index] All Phase Successfully Completed.");
}

main().catch((error) => {
  logger.error("Error Running Task:", error);
});