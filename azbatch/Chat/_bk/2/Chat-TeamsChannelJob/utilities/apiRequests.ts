import * as dotenv from 'dotenv';

dotenv.config();

const TEAMS_CHANNEL_MESSAGES_SEARCH_SIZE: string = process.env['TEAMS_CHANNEL_MESSAGES_SEARCH_SIZE'] ?? '50';
const TEAMS_CHANNEL_MESSAGES_REPLIES_SEARCH_SIZE: string = process.env['TEAMS_CHANNEL_MESSAGES_REPLIES_SEARCH_SIZE'] ?? '50';

interface TeamsChannelRequest {
  id: string;
  method: string;
  url: string;
}

export function createJoinedTeamsRequests(userId: string): TeamsChannelRequest[] {
  if (!userId) return [];

  return [{
    id: userId,
    method: 'GET',
    url: `/users/${userId}/joinedTeams`
  }];
}

export function createTeamsChannelRequests(teamId: string): TeamsChannelRequest[] {
  if (!teamId) return [];

  return [{
    id: teamId,
    method: 'GET',
    url: `/teams/${teamId}/channels`
  }];
}

export function createTeamsChannelMessagesRequests(teamId: string, channelId: string): TeamsChannelRequest[] {
  if (!teamId && !channelId) return [];

  return [{
    id: `team_${teamId}_channel_${channelId}`,
    method: 'GET',
    url: `/teams/${teamId}/channels/${channelId}/messages?$top=${TEAMS_CHANNEL_MESSAGES_SEARCH_SIZE}`
  }];
}

export function createTeamsChannelMessagesRepliesRequests(teamId: string, channelId: string, messageId: string): TeamsChannelRequest[] {
  if (!teamId && !channelId && !messageId) return [];

  return [{
    id: `team_${teamId}_channel_${channelId}_message_${messageId}`,
    method: 'GET',
    url: `/teams/${teamId}/channels/${channelId}/messages/${messageId}/replies?$top=${TEAMS_CHANNEL_MESSAGES_REPLIES_SEARCH_SIZE}`
  }];
}

export function createTeamsMembersRequests(teamId: string): TeamsChannelRequest[] {
  if (!teamId) return [];

  return [{
    id: teamId,
    method: 'GET',
    url: `/teams/${teamId}/members`
  }];
}