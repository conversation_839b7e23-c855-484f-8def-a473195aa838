import { Con<PERSON><PERSON>, CosmosClient } from "@azure/cosmos";
import { IBatchResponseData, ITeamsChannelMessage, ITeamsMembers } from "./models";
import { CustomLogger } from "./log";
import * as dotenv from "dotenv";

dotenv.config();

const cosmosDBEndpoint = process.env["COSMOS_DB_ENDPOINT"];
const cosmosDBKey = process.env["COSMOS_DB_KEY"];
const databaseName = process.env["COSMOS_DB_DATABASE"];
const containerName = process.env["COSMOS_DB_CONTAINER"];

let cosmosClient: CosmosClient | null = null;

function getClient(logger: CustomLogger) {
  try {
    if (!cosmosDBEndpoint || !cosmosDBKey) {
      throw new Error("[CosmosDB:getClient] cosmosDBEndpoint and cosmosDBKey must be defined");
    }
    if (!cosmosClient) {
      logger.log("[CosmosDB:getClient] Initializing Client Connection...");
      cosmosClient = new CosmosClient({
        endpoint: cosmosDBEndpoint,
        key: cosmosDBK<PERSON>,
      });
      logger.log("[CosmosDB:getClient] Client Initialized Successfully");
    } else {
      logger.log("[CosmosDB:getClient] Reusing Existing Connection");
    }

    return cosmosClient;
  } catch (error) {
    logger.log(`[CosmosDB:getClient] Error Initialization: ${error}`);
    throw error;
  }
}

export async function validateCosmosDBConnection(
  logger: CustomLogger
): Promise<void> {
  try {
    if (!databaseName || !containerName) {
      throw new Error("[CosmosDB:validateCosmosDBConnection] databaseName and containerName must be defined");
    }
    const client = getClient(logger);

    // Test General Connection
    logger.log(`[CosmosDB:validateCosmosDBConnection] Testing Cosmos DB Connection...`);
    const { resources: databases } = await client.databases
      .readAll()
      .fetchAll();
    logger.log(`[CosmosDB:validateCosmosDBConnection] Successfully Connected! Found ${databases.length} Databases`);

    // Test Database Connection
    const database = client.database(databaseName);
    const dbResponse = await database.read();
    if (dbResponse.resource) {
      logger.log(`[CosmosDB:validateCosmosDBConnection] Connected to Database: ${dbResponse.resource.id}`);
    } else {
      throw new Error("[CosmosDB:validateCosmosDBConnection] Database resource is undefined");
    }

    // Test Container Connections
    const container = database.container(containerName);
    const containerResponse = await container.read();
    if (containerResponse.resource) {
      logger.log(`[CosmosDB:validateCosmosDBConnection] Connected to Container: ${containerResponse.resource.id}`);
    } else {
      throw new Error("[CosmosDB:validateCosmosDBConnection] Container resource is undefined");
    }

    // Count Container Details
    const {
      resources: [count],
    } = await container.items.query("SELECT VALUE COUNT(1) FROM c").fetchAll();
    logger.log(`[CosmosDB:validateCosmosDBConnection] Container has: ${count} Items`);
  } catch (error) {
    logger.log(`[CosmosDB:validateCosmosDBConnection] Error Connection: ${error}`);
    throw error;
  }
}

export async function insertTeamsChannelMessages(
  logger: CustomLogger,
  dataTeamsChannelMessages: IBatchResponseData[]
): Promise<void> {

  // logger.log(`[CosmosDB:insertTeamsChannelMessages] dataTeamsChannelMessages: ${JSON.stringify(dataTeamsChannelMessages)}`); // !!!

  try {
    if (!databaseName || !containerName) {
      throw new Error("[CosmosDB:insertTeamsChannelMessages] databaseName and containerName must be defined");
    }

    const client = getClient(logger);
    const container = client.database(databaseName).container(containerName);

    const modifiedDataTeamsChannelMessages: ITeamsChannelMessage[][] = dataTeamsChannelMessages
      .map((message) => message.body?.value || [])
      .filter(Array.isArray);

    const insertedCount = await processTeamsChannelMessages(container, modifiedDataTeamsChannelMessages, logger);

    logger.log(`[CosmosDB:insertTeamsChannelMessages] Inserted ${insertedCount} New TeamsChatMessages to Cosmos DB`);
    const totalMessageCount = modifiedDataTeamsChannelMessages.reduce((count, array) => count + (array ? array.length : 0),0 );
    logger.log(`[CosmosDB:insertTeamsChannelMessages] Skipped ${totalMessageCount - insertedCount} Existing TeamsChatMessages`);
  } catch (error) {
    logger.log(`[CosmosDB:insertTeamsChannelMessages] Error processing system messages: ${error}`);
    throw error;
  }
}

async function processTeamsChannelMessages(
  container: Container,
  modifiedDataTeamsChannelMessages: ITeamsChannelMessage[][],
  logger: CustomLogger
): Promise<number> {
  let insertedCount = 0;

  logger.log(`[CosmosDB:processMessages] Total modifiedDataTeamsChannelMessages: ${modifiedDataTeamsChannelMessages.length}`);
  // logger.log(`[CosmosDB:processMessages] modifiedDataTeamsChannelMessages: ${JSON.stringify(modifiedDataTeamsChannelMessages)}`); // !!!

  for (const messageArray of modifiedDataTeamsChannelMessages) {
    for (const message of messageArray) {
      if (!message.id) {
        logger.log(`[CosmosDB:Process] Error: message.id is undefined for message: ${JSON.stringify(message.id)}`);
        continue;
      }
      const querySpec = {
        query: "SELECT * FROM c WHERE c.id = @id",
        parameters: [{ name: "@id", value: message.id }]
      };
      try {
        const { resources: existingMessages } = await container.items.query(querySpec).fetchAll();
        if (existingMessages.length === 0) {
          await container.items.create(message);
          insertedCount++;
        }
      } catch (error) {
        logger.log(`[CosmosDB:Process] Error processing message: ${error}`);
      }
    }
  }
  return insertedCount;
}

export async function updateTeamsMembers(
  logger: CustomLogger,
  dataTeamMembers: IBatchResponseData[]
): Promise<void> {

  // logger.log(`[CosmosDB:updateTeamsMembers] dataTeamMembers: ${JSON.stringify(dataTeamMembers)}`); // !!!

  try {
    if (!databaseName || !containerName) {
      throw new Error("[CosmosDB:updateTeamsMembers] databaseName and containerName must be defined");
    }

    const client = getClient(logger);
    const container = client.database(databaseName).container(containerName);

    const modifiedDataTeamMembers = dataTeamMembers
    .map((team) => ({
      teamId: team.id,
      members: team.body?.value || []
    }))
  
    const updatedMessagesCount = await processTeamsMembers(container, modifiedDataTeamMembers, logger);
    logger.log(`[CosmosDB:updateTeamsMembers] Updated ${updatedMessagesCount} Messages to Cosmos DB`);
  } catch (error) {
    logger.log(`[CosmosDB:updateTeamsMembers] Error processing system messages: ${error}`);
    throw error;
  }
}

async function processTeamsMembers(
  container: Container,
  modifiedDataTeamMembers: any[],
  logger: CustomLogger
): Promise<number> {
  let updatedCount = 0;
  
  logger.log(`[CosmosDB:processTeamsMembers] Total modifiedDataTeamMembers: ${modifiedDataTeamMembers.length}`);
  // logger.log(`[CosmosDB:processTeamsMembers] modifiedDataTeamMembers: ${JSON.stringify(modifiedDataTeamMembers)}`); // !!!

  // Create a map of team IDs to user IDs
  const teamToUserIdsMap: {[teamId: string]: string[]} = {};
  
  // Process each team entry
  for (const teamEntry of modifiedDataTeamMembers) {
    const teamId = teamEntry.teamId;
    const members = teamEntry.members;
    if (!teamToUserIdsMap[teamId]) {
      teamToUserIdsMap[teamId] = [];
    }
    // Extract user IDs from each member
    for (const member of members) {
      if (member.userId && !teamToUserIdsMap[teamId].includes(member.userId)) {
        teamToUserIdsMap[teamId].push(member.userId);
      }
    }
  }
  logger.log(`[CosmosDB:processTeamsMembers] Team to User ID's Map: ${JSON.stringify(teamToUserIdsMap)}`);
  // Process each team
  for (const teamId in teamToUserIdsMap) {
    const userIds = teamToUserIdsMap[teamId];
    // Query for messages from this team
    const querySpec = {
      query: "SELECT * FROM c WHERE c.channelIdentity.teamId = @teamId",
      parameters: [
        {
          name: "@teamId",
          value: teamId
        }
      ]
    };
    const { resources: messages } = await container.items.query(querySpec).fetchAll();
    logger.log(`[CosmosDB:processTeamsMembers] Found ${messages.length} Messages for Team: ${teamId}`);
    // Update each message
    for (const message of messages) {
      message.security_user_id = userIds;
      try {
        await container.items.upsert(message);
        updatedCount++;
      } catch (error) {
        logger.log(`[CosmosDB:processTeamsMembers] Error Updating Message: ${message.id}: ${error}`);
      }
    }
  }
  return updatedCount;
}