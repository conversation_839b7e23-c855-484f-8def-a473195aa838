import "isomorphic-fetch";
import { TokenCredential } from '@azure/core-auth';
import { ClientSecretCredential } from '@azure/identity';
import { BatchRequestData, Client } from '@microsoft/microsoft-graph-client';
import { 
  createJoinedTeamsRequests,
  createTeamsChannelRequests,
  createTeamsChannelMessagesRequests,
  createTeamsChannelMessagesRepliesRequests,
  createTeamsMembersRequests 
} from '../utilities/apiRequests';
import { splitArrayIntoChunks } from '../utilities/array';
import {
  fetchGroupUsers,
  fetchJsonBatchForTeamsChannel,
  fetchJsonBatchForTeamsChannelReplies
} from '../utilities/graph';
import { logLongArray, CustomLogger } from '../utilities/log';
import {
  IBatchResponseData,
  IBatchResponses,
  ITeamsChannelMessage,
  ITeamsUniqueChannelMessage,
  ITeamsMembers
} from '../utilities/models';
import { encrypt } from '../utilities/encryption';
import {
  insertTeamsChannelMessages,
  updateTeamsMembers
} from '../utilities/cosmos';

// GraphAPIのバッチリクエスト最大数
const MAX_GRAPH_API_JOINED_TEAMS_BATCH_COUNTS = parseInt(process.env.MAX_GRAPH_API_JOINED_TEAMS_BATCH_COUNTS ?? '20');
const MAX_GRAPH_API_TEAMS_CHANNEL_BATCH_COUNTS = parseInt(process.env.MAX_GRAPH_API_TEAMS_CHANNEL_BATCH_COUNTS ?? '20');
const MAX_GRAPH_API_TEAMS_CHANNEL_MESSAGES_BATCH_COUNTS = parseInt(process.env.MAX_GRAPH_API_TEAMS_CHANNEL_MESSAGES_BATCH_COUNTS ?? '20');
const MAX_GRAPH_API_TEAMS_CHANNEL_MESSAGES_REPLIES_BATCH_COUNTS = parseInt(process.env.MAX_GRAPH_API_TEAMS_CHANNEL_MESSAGES_REPLIES_BATCH_COUNTS ?? '20');
const MAX_GRAPH_API_TEAMS_MEMBERS_BATCH_COUNTS = parseInt(process.env.MAX_GRAPH_API_TEAMS_MEMBERS_BATCH_COUNTS ?? '20');
const MAX_TEAMS_CHANNEL_MESSAGES_CHUNK_SIZE = parseInt(process.env.MAX_TEAMS_CHANNEL_MESSAGES_CHUNK_SIZE ?? '50');
const MAX_TEAMS_CHANNEL_MESSAGES_REPLIES_CHUNK_SIZE = parseInt(process.env.MAX_TEAMS_CHANNEL_MESSAGES_REPLIES_CHUNK_SIZE ?? '50');
const MAX_TEAMS_MEMBERS_CHUNK_SIZE = parseInt(process.env.MAX_TEAMS_MEMBERS_CHUNK_SIZE ?? '50');


type Logger = CustomLogger;

/******************************************************* ========== PHASE 1 - START ========== *******************************************************/
/**
 * credentialを作成
 * 必須パラメータが存在しない場合はundefinedを返却
 */
export function createCredential(
  tenantId: string,
  clientId: string,
  clientSecret: string,
): TokenCredential | undefined {

  // 必須パラメータが存在しない場合はundefined
  if (!tenantId || !clientId || !clientSecret) {
    return undefined;
  }
  return new ClientSecretCredential(tenantId, clientId, clientSecret);
}
/******************************************************* ========== PHASE 1 - END ========== *********************************************************/
/******************************************************* ========== PHASE 2 - START ========== *******************************************************/
/**
 * グループ内に所属する全ユーザーのうち、カスタムアプリを所持するユーザーだけを抽出する
 * @param logger
 * @param client
 * @param groupId
 * @param teamsAppId
 */
export async function fetchTargetUsers(
  logger: Logger,
  client: Client,
  groupId: string,
  teamsAppId: string,
): Promise<IBatchResponseData[]> {

  // 対象とするユーザーを取得するためのグループID
  if (!groupId || !teamsAppId) {
    // 存在しない場合は異常終了
    return Promise.reject('NO_REQUIRED_IDS');
  }

  // グループ内のユーザー一覧を取得
  const users = await fetchGroupUsers(client, groupId);
  logLongArray(logger, '[Impl:fetchTargetUsers] AllGroupUsers.id', 800, users.map((u) => u.id));

  if (users.length === 0) {
    // 0件の場合はここで終了
    return Promise.resolve([]);
  }
  return users;
}
/******************************************************* ========== PHASE 2 - END ========== *********************************************************/
/******************************************************* ========== PHASE 4 - START ========== *******************************************************/
export async function processJoinedTeams(
  logger: Logger,
  client: Client,
  targetUsersData: IBatchResponseData[],
): Promise<Array<{teamId: string, teamDisplayName: string}>> {

  logger.log(`[Impl:processJoinedTeams] Total targetUsersData to Process: ${targetUsersData.length}`);
  // logger.log(`[Impl:processJoinedTeams] targetUsersData: ${JSON.stringify(targetUsersData)}`);  // !!!

  const userJoinedTeamsBatchRequestsCreated: BatchRequestData[] = targetUsersData
    .filter(data => data.id)
    .flatMap((data) =>
      createJoinedTeamsRequests(data?.id ?? '').map((request) => ({
        id: request.id,
        method: request.method,
        url: request.url
      }))
    );

  logger.log(`[Impl:processJoinedTeams] Total GraphAPI - userJoinedTeamsBatchRequestsCreated: ${userJoinedTeamsBatchRequestsCreated.length} | MAX_GRAPH_API_JOINED_TEAMS_BATCH_COUNTS: ${MAX_GRAPH_API_JOINED_TEAMS_BATCH_COUNTS}`);
  const userSplitJoinedTeamsBatchRequests = splitArrayIntoChunks(userJoinedTeamsBatchRequestsCreated, MAX_GRAPH_API_JOINED_TEAMS_BATCH_COUNTS);
  logger.log(`[Impl:processJoinedTeams] Total JoinedTeams Split Batch: ${userSplitJoinedTeamsBatchRequests.length}`);
  // logger.log(`[Impl:processJoinedTeams] userSplitJoinedTeamsBatchRequests: ${JSON.stringify(userSplitJoinedTeamsBatchRequests)}`);  // !!!

  const uniqueTeamsMap = new Map<string, {teamId: string, teamDisplayName: string}>();

  // Process all batch requests
  const totalJoinedTeamsBatchRequests = userSplitJoinedTeamsBatchRequests.length;
  for (let i = 0; i < totalJoinedTeamsBatchRequests; i++) {
    try {
      const currentJoinedTeamsBatchRequests = i + 1;

      logger.log(`\n==== START - BATCH | JOINED_TEAMS: (Batch Request: ${currentJoinedTeamsBatchRequests} of ${totalJoinedTeamsBatchRequests}) ====`);
      const currentUserSplitJoinedTeamsBatchRequests = userSplitJoinedTeamsBatchRequests[i];
      logger.log(`[Impl:processJoinedTeams] Processing Batch...`);

      // Process 1 batch request at a time
      const batchResultJoinedTeams = await fetchJsonBatchForTeamsChannel(logger, client, currentUserSplitJoinedTeamsBatchRequests);
      // *** View Raw Response
      // logger.log(`[Impl:processJoinedTeams] *** batchResultJoinedTeams == ${JSON.stringify(batchResultJoinedTeams)} === (Batch Request: ${currentJoinedTeamsBatchRequests} of ${totalJoinedTeamsBatchRequests})`);  // !!!

      if (!batchResultJoinedTeams.responses || batchResultJoinedTeams.responses.length === 0) {
        logger.log(`[Impl:processJoinedTeams] No Responses in Batch === (Batch Request: ${currentJoinedTeamsBatchRequests} of ${totalJoinedTeamsBatchRequests})`);
        continue;
      }
      // Extract the Unique Teams
      for (const response of batchResultJoinedTeams.responses) {
        if (response.status !== 200 || !response.body?.value) continue;
        // Process each team in the response
        for (const team of response.body.value) {
          const teamIdVal = team.id;
          const teamDisplayNameVal = (team as { displayName: string }).displayName;
          // Get teamId and teamDisplayName
          if (teamIdVal && !uniqueTeamsMap.has(teamIdVal)) {
            uniqueTeamsMap.set(teamIdVal, {
              teamId: teamIdVal,
              teamDisplayName: teamDisplayNameVal
            });
          }
        }
      }

      // Clear batch responses AFTER processing ALL users
      batchResultJoinedTeams.responses = [];
      // Free memory for this batch
      userSplitJoinedTeamsBatchRequests[i] = [];
      // Add a delay between batches
      await new Promise(resolve => setTimeout(resolve, 3000));

      logger.log(`==== END - BATCH | JOINED_TEAMS: (Batch Request: ${currentJoinedTeamsBatchRequests} of ${totalJoinedTeamsBatchRequests}) ====\n`);

    } catch (error) {
      logger.error(`[Impl:processJoinedTeams] Error Processing Batch Starting at Index ${i}: ${error}`);
      continue;
    }
    global.gc && global.gc();
  }

  const uniqueTeams = Array.from(uniqueTeamsMap.values());
  userSplitJoinedTeamsBatchRequests.length = 0;

  return uniqueTeams;
}
/******************************************************* ========== PHASE 4 - END ========== *********************************************************/
/******************************************************* ========== PHASE 5 - START ========== *******************************************************/
export async function processTeamsChannels(
  logger: Logger,
  client: Client,
  uniqueTeamsData: Array<{ teamId: string }>,
): Promise<Array<{teamId: string, channelId: string, channelDisplayName: string}>> {

  logger.log(`[Impl:processTeamsChannels] Total uniqueTeamsData to Process: ${uniqueTeamsData.length}`);
  // logger.log(`[Impl:processTeamsChannels] uniqueTeamsData: ${JSON.stringify(uniqueTeamsData)}`);  // !!!

  const userTeamsChannelBatchRequestsCreated: BatchRequestData[] = uniqueTeamsData
    .filter(data => data.teamId)
    .flatMap((data) =>
      createTeamsChannelRequests(data?.teamId ?? '').map((request) => ({
        id: request.id,
        method: request.method,
        url: request.url
      }))
    );

  logger.log(`[Impl:processTeamsChannels] Total GraphAPI - userTeamsChannelBatchRequestsCreated: ${userTeamsChannelBatchRequestsCreated.length} | MAX_GRAPH_API_TEAMS_CHANNEL_BATCH_COUNTS: ${MAX_GRAPH_API_TEAMS_CHANNEL_BATCH_COUNTS}`);
  const userSplitTeamsChannelBatchRequests = splitArrayIntoChunks(userTeamsChannelBatchRequestsCreated, MAX_GRAPH_API_TEAMS_CHANNEL_BATCH_COUNTS);
  logger.log(`[Impl:processTeamsChannels] Total TeamsChannel Split Batch: ${userSplitTeamsChannelBatchRequests.length}`);
  // logger.log(`[Impl:processTeamsChannels] userSplitTeamsChannelBatchRequests: ${JSON.stringify(userSplitTeamsChannelBatchRequests)}`); // !!!

  const uniqueTeamsChannelMap = new Map<string, {teamId: string, channelId: string, channelDisplayName: string}>();

  // Process all batch requests
  const totalTeamsChannelBatchRequests = userSplitTeamsChannelBatchRequests.length;
  for (let i = 0; i < totalTeamsChannelBatchRequests; i++) {
    try {
      const currentTeamsChannelBatchRequests = i + 1;

      logger.log(`\n==== START - BATCH | TEAMS_CHANNEL: (Batch Request: ${currentTeamsChannelBatchRequests} of ${totalTeamsChannelBatchRequests}) ====`);
      const currentUserSplitTeamsChannelBatchRequests = userSplitTeamsChannelBatchRequests[i];
      logger.log(`[Impl:processTeamsChannels] Processing Batch...`);

      // Process 1 batch request at a time
      const batchResultTeamsChannel = await fetchJsonBatchForTeamsChannel(logger, client, currentUserSplitTeamsChannelBatchRequests);
      // *** View Raw Response
      // logger.log(`[Impl:processTeamsChannels] *** batchResultTeamsChannel == ${JSON.stringify(batchResultTeamsChannel)} === (Batch Request: ${currentTeamsChannelBatchRequests} of ${totalTeamsChannelBatchRequests})`); // !!!

      if (!batchResultTeamsChannel.responses || batchResultTeamsChannel.responses.length === 0) {
        logger.log(`[Impl:processTeamsChannels] No Responses in Batch === (Batch Request: ${currentTeamsChannelBatchRequests} of ${totalTeamsChannelBatchRequests})`);
        continue;
      }
      // Extract the Unique Teams Channels
      for (const response of batchResultTeamsChannel.responses) {
        if (response.status !== 200 || !response.body?.value) continue;
        // Extract the response ID
        const teamIdVal = response.id;
        // Process each team in the response
        for (const channel of response.body.value) {
          const channelIdVal = channel.id;
          const channelDisplayNameVal = (channel as { displayName: string }).displayName;
          // Get teamId, channelId and channelDisplayName
          if (channelIdVal && !uniqueTeamsChannelMap.has(channelIdVal)) {
            uniqueTeamsChannelMap.set(channelIdVal, {
              teamId: teamIdVal ?? '',
              channelId: channelIdVal,
              channelDisplayName: channelDisplayNameVal
            });
          }
        }
      }

      // Clear batch responses AFTER processing ALL users
      batchResultTeamsChannel.responses = [];
      // Free memory for this batch
      userSplitTeamsChannelBatchRequests[i] = [];
      // Add a delay between batches
      await new Promise(resolve => setTimeout(resolve, 3000));

      logger.log(`==== END - BATCH | TEAMS_CHANNEL: (Batch Request: ${currentTeamsChannelBatchRequests} of ${totalTeamsChannelBatchRequests}) ====\n`);

    } catch (error) {
      logger.error(`[Impl:processTeamsChannels] Error Processing Batch Starting at Index ${i}: ${error}`);
      continue;
    }
    global.gc && global.gc();
  }
  
  const uniqueTeamsChannel = Array.from(uniqueTeamsChannelMap.values());
  userSplitTeamsChannelBatchRequests.length = 0;
  
  return uniqueTeamsChannel;
}
/******************************************************* ========== PHASE 5 - END ========== *********************************************************/
/******************************************************* ========== PHASE 6 - START ========== *******************************************************/
export async function processTeamsChannelMessages(
  logger: Logger,
  client: Client,
  uniqueTeamsChannelsData: Array<{ teamId: string, channelId: string }>,
): Promise<ITeamsUniqueChannelMessage[]> {

  logger.log(`[Impl:processTeamsChannelMessages] Total uniqueTeamsChannelsData to Process: ${uniqueTeamsChannelsData.length}`);
  // logger.log(`[Impl:processTeamsChannelMessages] uniqueTeamsChannelsData: ${JSON.stringify(uniqueTeamsChannelsData)}`); // !!!

  const userTeamsChannelBatchRequestsCreated: BatchRequestData[] = uniqueTeamsChannelsData
    .filter(data => data.teamId && data.channelId)
    .flatMap((data) =>
      createTeamsChannelMessagesRequests(data?.teamId ?? '', data?.channelId ?? '').map((request) => ({
        id: request.id,
        method: request.method,
        url: request.url
      }))
    );

  logger.log(`[Impl:processTeamsChannelMessages] Total GraphAPI - userTeamsChannelBatchRequestsCreated: ${userTeamsChannelBatchRequestsCreated.length} | MAX_GRAPH_API_TEAMS_CHANNEL_MESSAGES_BATCH_COUNTS: ${MAX_GRAPH_API_TEAMS_CHANNEL_MESSAGES_BATCH_COUNTS}`);
  const userSplitTeamsChannelMessagesBatchRequests = splitArrayIntoChunks(userTeamsChannelBatchRequestsCreated, MAX_GRAPH_API_TEAMS_CHANNEL_MESSAGES_BATCH_COUNTS);
  logger.log(`[Impl:processTeamsChannelMessages] Total TeamsChannel Split Batch: ${userSplitTeamsChannelMessagesBatchRequests.length}`);
  // logger.log(`[Impl:processTeamsChannelMessages] userSplitTeamsChannelMessagesBatchRequests: ${JSON.stringify(userSplitTeamsChannelMessagesBatchRequests)}`); // !!!

  const uniqueTeamsChannelMessagesMap = new Map<string, ITeamsUniqueChannelMessage>();

  // Process all batch requests
  const totalTeamsChannelMessagesBatchRequests = userSplitTeamsChannelMessagesBatchRequests.length;
  for (let i = 0; i < totalTeamsChannelMessagesBatchRequests; i++) {
    try {
      const currentTeamsChannelMessagesBatchRequests = i + 1;

      logger.log(`\n==== START - BATCH | TEAMS_CHANNEL_MESSAGES: (Batch Request: ${currentTeamsChannelMessagesBatchRequests} of ${totalTeamsChannelMessagesBatchRequests}) ====`);
      const currentUserSplitTeamsChannelMessagesBatchRequests = userSplitTeamsChannelMessagesBatchRequests[i];
      logger.log(`[Impl:processTeamsChannelMessages] Processing Batch...`);

      // Process 1 batch request at a time
      const batchResultTeamsChannelMessages = await fetchJsonBatchForTeamsChannel(logger, client, currentUserSplitTeamsChannelMessagesBatchRequests);
      // // *** View Raw Response
      // logger.log(`[Impl:processTeamsChannelMessages] *** batchResultTeamsChannelMessages == ${JSON.stringify(batchResultTeamsChannelMessages)} === (Batch Request: ${currentTeamsChannelMessagesBatchRequests} of ${totalTeamsChannelMessagesBatchRequests})`);  // !!!

      if (!batchResultTeamsChannelMessages.responses || batchResultTeamsChannelMessages.responses.length === 0) {
        logger.log(`[Impl:processTeamsChannelMessages] No Responses in Batch === (Batch Request: ${currentTeamsChannelMessagesBatchRequests} of ${totalTeamsChannelMessagesBatchRequests})`);
        continue;
      }

      // Extract the Unique Teams Channels
      for (const response of batchResultTeamsChannelMessages.responses) {
        if (response.status !== 200 || !response.body?.value) continue;
        // // Extract the response ID
        // const teamChannelIdVal = response.id;
        // Process each team in the response
        for (const messageDetail of response.body.value) {
          const messageIdVal =(messageDetail as ITeamsChannelMessage).id;
          const channelIdentity = (messageDetail as ITeamsChannelMessage).channelIdentity;
          const teamIdVal = channelIdentity?.teamId;
          const channelIdVal = channelIdentity?.channelId;
          // Get teamChannelId, messageId, teamId, channelId
          if (messageIdVal && !uniqueTeamsChannelMessagesMap.has(messageIdVal)) {
            uniqueTeamsChannelMessagesMap.set(messageIdVal, {
              // teamChannelId: teamChannelIdVal ?? '',
              messageId: messageIdVal ?? '',
              teamId: teamIdVal ?? '',
              channelId: channelIdVal ?? '',
            });
          }
        }
      }

      await processBatchResultTeamsChannelMessages(logger, batchResultTeamsChannelMessages, currentTeamsChannelMessagesBatchRequests, totalTeamsChannelMessagesBatchRequests);

      // Clear batch responses AFTER processing ALL users
      batchResultTeamsChannelMessages.responses = [];
      // Free memory for this batch
      userSplitTeamsChannelMessagesBatchRequests[i] = [];
      // Add a delay between batches
      await new Promise(resolve => setTimeout(resolve, 3000));

      logger.log(`==== END - BATCH | TEAMS_CHANNEL_MESSAGES: (Batch Request: ${currentTeamsChannelMessagesBatchRequests} of ${totalTeamsChannelMessagesBatchRequests}) - COMPLETE) ====\n`);
    } catch (error) {
      logger.error(`[Impl:processTeamsChannelMessages] Error Processing Batch Starting at Index ${i}: ${error}`);
      continue;
    }
    global.gc && global.gc();
  }
  const uniqueTeamsChannelMessages = Array.from(uniqueTeamsChannelMessagesMap.values());
  userSplitTeamsChannelMessagesBatchRequests.length = 0;

  return uniqueTeamsChannelMessages;
}

async function processBatchResultTeamsChannelMessages(
  logger: Logger,
  batchResultTeamsChannelMessages: IBatchResponses,
  currentTeamsChannelMessagesBatchRequests: number,
  totalTeamsChannelMessagesBatchRequests: number
): Promise<void> {

  const totalBatchResultTeamsChannelMessages = batchResultTeamsChannelMessages.responses?.length ?? 0;
  for (let j = 0; j < totalBatchResultTeamsChannelMessages; j++) {
    const currentBatchResultTeamsChannelMessages = j + 1;
    const batchResultTeamsChannelMessagesResponses = (batchResultTeamsChannelMessages.responses ?? [])[j];
    const teamsChannelId = batchResultTeamsChannelMessagesResponses.id;

    logger.log(`\n+=+= START - PROCESSING BATCH | TEAMS_CHANNEL_MESSAGES: (teamsChannelId: ${teamsChannelId} / Batch Result: ${currentBatchResultTeamsChannelMessages} of ${totalBatchResultTeamsChannelMessages} / Batch Request: ${currentTeamsChannelMessagesBatchRequests} of ${totalTeamsChannelMessagesBatchRequests}) +=+=`);

    if (!batchResultTeamsChannelMessagesResponses || batchResultTeamsChannelMessagesResponses.status !== 200) {
      const batchResultTeamsChatMessagesResponses_id = batchResultTeamsChannelMessagesResponses?.id ?? 'teamsChannelId-undefined';
      logger.log(`[Impl:processBatchResultTeamsChannelMessages] Skipping teamsChannelId ${batchResultTeamsChatMessagesResponses_id} with Error Data: ${batchResultTeamsChannelMessagesResponses?.status} === (teamsChannelId: ${teamsChannelId} / Batch Result: ${currentBatchResultTeamsChannelMessages} of ${totalBatchResultTeamsChannelMessages} / Batch Request: ${currentTeamsChannelMessagesBatchRequests} of ${totalTeamsChannelMessagesBatchRequests})`);
      continue;
    }
 
    const singleResultTeamsChannelMessages = { responses: [batchResultTeamsChannelMessagesResponses] };
    // logger.info(`[Impl:processBatchResultTeamsChannelMessages] singleResultTeamsChannelMessages == ${JSON.stringify(singleResultTeamsChannelMessages)} === (teamsChannelId: ${teamsChannelId} / Batch Result: ${currentBatchResultTeamsChannelMessages} of ${totalBatchResultTeamsChannelMessages} / Batch Request: ${currentTeamsChannelMessagesBatchRequests} of ${totalTeamsChannelMessagesBatchRequests})`); // !!!

    const modifiedTeamsChannelMessagesChunk = processSingleResultTeamsChannelMessages([singleResultTeamsChannelMessages], logger);
    // // *** View Raw Response
    // logger.info(`[Impl:processBatchResultTeamsChannelMessages] *** modifiedTeamsChannelMessagesChunk == ${JSON.stringify(modifiedTeamsChannelMessagesChunk)} === (teamsChannelId: ${teamsChannelId} / Batch Result: ${currentBatchResultTeamsChannelMessages} of ${totalBatchResultTeamsChannelMessages} / Batch Request: ${currentTeamsChannelMessagesBatchRequests} of ${totalTeamsChannelMessagesBatchRequests})`); // !!!

    const totalModifiedTeamsChannelMessagesChunk = modifiedTeamsChannelMessagesChunk.length;
    // Insert each messageChunk
    for (let k = 0; k < totalModifiedTeamsChannelMessagesChunk; k++) {
      const messageChunk = modifiedTeamsChannelMessagesChunk[k];
      const currentModifiedUserTeamsChatMessagesChunk = k + 1;
      if (!messageChunk) {
        logger.info(`[Impl:processBatchResultTeamsChannelMessages] Skipping Undefined TeamsChatMessagesChunk at Index: ${k} === (teamsChannelId: ${teamsChannelId} / Batch Result: ${currentBatchResultTeamsChannelMessages} of ${totalBatchResultTeamsChannelMessages} / Batch Request: ${currentTeamsChannelMessagesBatchRequests} of ${totalTeamsChannelMessagesBatchRequests})`);
        continue;
      }
      logger.log(`---- START - INSERT COSMOS_DB | TEAMS_CHANNEL_MESSAGES: (Chunk: ${currentModifiedUserTeamsChatMessagesChunk} of ${totalModifiedTeamsChannelMessagesChunk} with ${messageChunk.body?.value?.length} ChannelMessages Inside) === (teamsChannelId: ${teamsChannelId} / Batch Result: ${currentBatchResultTeamsChannelMessages} of ${totalBatchResultTeamsChannelMessages} / Batch Request: ${currentTeamsChannelMessagesBatchRequests} of ${totalTeamsChannelMessagesBatchRequests}) ----`);
      try {
        logger.info(`[Impl:processBatchResultTeamsChannelMessages] Inserting TeamsChannelMessages...`);
        await insertTeamsChannelMessages(logger, [messageChunk]);
        logger.info(`[Impl:processBatchResultTeamsChannelMessages] Successfully Inserted TeamsChannelMessages...`);
      } catch (error) {
        logger.error(`[Impl:processBatchResultTeamsChannelMessages] Failed Inserting: ${error} (Chunk: ${currentModifiedUserTeamsChatMessagesChunk} of ${totalModifiedTeamsChannelMessagesChunk} with ${messageChunk.body?.value?.length} ChannelMessages Inside) === (teamsChannelId: ${teamsChannelId} / Batch Result: ${currentBatchResultTeamsChannelMessages} of ${totalBatchResultTeamsChannelMessages} / Batch Request: ${currentTeamsChannelMessagesBatchRequests} of ${totalTeamsChannelMessagesBatchRequests})`);
        continue;
      }
      modifiedTeamsChannelMessagesChunk[k] = {} as IBatchResponseData;
      logger.log(`---- END - INSERT COSMOS_DB | TEAMS_CHANNEL_MESSAGES: (Chunk: ${currentModifiedUserTeamsChatMessagesChunk} of ${totalModifiedTeamsChannelMessagesChunk} with ${messageChunk.body?.value?.length} ChannelMessages Inside) === (teamsChannelId: ${teamsChannelId} / Batch Result: ${currentBatchResultTeamsChannelMessages} of ${totalBatchResultTeamsChannelMessages} / Batch Request: ${currentTeamsChannelMessagesBatchRequests} of ${totalTeamsChannelMessagesBatchRequests}) ----`);
    }

    modifiedTeamsChannelMessagesChunk.length = 0;
    global.gc && global.gc();
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    logger.log(`+=+= END - PROCESSING BATCH | TEAMS_CHANNEL_MESSAGES: (teamsChannelId: ${teamsChannelId} / Batch Result: ${currentBatchResultTeamsChannelMessages} of ${totalBatchResultTeamsChannelMessages} / Batch Request: ${currentTeamsChannelMessagesBatchRequests} of ${totalTeamsChannelMessagesBatchRequests}) +=+=`);
  }
}

function processSingleResultTeamsChannelMessages(
  singleResTeamsChannelMessages: IBatchResponses[], 
  logger: Logger
): IBatchResponseData[] {
  const allProcessedData: IBatchResponseData[] = [];

  for (const result of singleResTeamsChannelMessages) {
    if (!result?.responses || !Array.isArray(result.responses)) {
      logger.log(`[Impl:processSingleResultTeamsChannelMessages] Skipping Invalid TeamsChannelMessages Result == ${JSON.stringify(result)}`);
      continue;
    }

    // Process each response in the batch
    for (const response of result.responses) {
      const totalTeamsChannelMessages = response.body?.value?.length || 0;
      logger.log(`[Impl:processSingleResultTeamsChannelMessages] Total TeamsChannelMessages in response.body.value: ${totalTeamsChannelMessages}`);

      const teamsChannelId = response.id ?? '';
      const allTeamsChatMessages = response.body?.value as ITeamsChannelMessage[];
      const transformedMessages = allTeamsChatMessages.map((item) => {
        const hasAttachments = 
          'attachments' in item && 
          Array.isArray((item as any).attachments) && 
          (item as any).attachments.length > 0;
        return {
          ...item,
          hasAttachments
        };
      });
      //logger.log(`[Impl:processSingleResultTeamsChannelMessages] transformedMessages: ${JSON.stringify(transformedMessages)}`);  // !!!
      const filteredMessages = transformedMessages.filter((message) => {
        return message.messageType !== 'systemEventMessage' && 
               message.messageType !== 'unknownFutureValue';
      });
      // logger.log(`[Impl:processSingleResultTeamsChannelMessages] filteredMessages: ${JSON.stringify(filteredMessages)}`);  // !!!
      const chunkSize = MAX_TEAMS_CHANNEL_MESSAGES_CHUNK_SIZE;

      for (let i = 0; i < filteredMessages.length; i += chunkSize) {
        const teamsChannelMessagesAfterChunk = filteredMessages.slice(i, i + chunkSize);
        // logger.info(`[Impl:processUserTeamsChannelMessagesByChunk] teamsChannelMessagesAfterChunk: ${JSON.stringify(teamsChannelMessagesAfterChunk)}`); // !!!
        processTeamsChannelMessagesChunk(teamsChannelId, teamsChannelMessagesAfterChunk, allProcessedData, logger);
        teamsChannelMessagesAfterChunk.length = 0;
      }
      allTeamsChatMessages.length = 0;
    }
  }
  // logger.info(`[Impl:processSingleResultTeamsChannelMessages] allProcessedData == ${JSON.stringify(allProcessedData)}`);
  return allProcessedData;
}

function processTeamsChannelMessagesChunk(
  teamsChannelId: string,
  teamsChannelMessagesAfterChunk: ITeamsChannelMessage[],
  allProcessedData: IBatchResponseData[],
  logger: Logger
): void {
  const processedValues: ITeamsChannelMessage[] = [];

  for (const item of teamsChannelMessagesAfterChunk) {
    if (hasEmptyRequiredFieldsMessages(item, logger)) {
      continue;
    }
    processedValues.push(createEncryptedMessages(item));
  }
  if (processedValues.length > 0) {
    allProcessedData.push({
      id: teamsChannelId,
      body: {
        value: processedValues
      }
    } as IBatchResponseData);

    logger.info(`[Impl:processTeamsChannelMessagesChunk] Successfully Modified TeamsChannelMessages: ${processedValues.length} TeamsChannelMessages Inside Chunk | MAX_TEAMS_CHANNEL_MESSAGES_CHUNK_SIZE: ${MAX_TEAMS_CHANNEL_MESSAGES_CHUNK_SIZE}`);
  }
}

function hasEmptyRequiredFieldsMessages(item: ITeamsChannelMessage, logger: Logger): boolean {
  // Check for empty values
  const isEmpty = (value: any) => value === null || value === undefined || value === "";
  const hasEmptyBody = !item.body || isEmpty(item.body.content);
  const hasEmptyChannelIdentity = !item.channelIdentity || isEmpty(item.channelIdentity.teamId) || isEmpty(item.channelIdentity.channelId);
  const hasEmptyValues = hasEmptyBody || hasEmptyChannelIdentity;
  if (hasEmptyValues) {
    logger.log(`[Impl:hasEmptyRequiredFields] Skipping message with empty required fields. Message ID: ${item.id}`);
    return true;
  }
  return false;
}

function createEncryptedMessages(item: ITeamsChannelMessage): ITeamsChannelMessage {
  return {
    security_user_id: [],
    id: item.id,
    kind: "Chat",
    replyToId: item.replyToId ? encrypt(item.replyToId) : null,
    messageType: item.messageType ? encrypt(item.messageType) : null,
    createdDateTime: item.createdDateTime ?? null,
    lastModifiedDateTime: item.lastModifiedDateTime ?? null,
    chatId: item.chatId ?? null,
    from: item.from ? {
      application: item.from.application ? {
        displayName: encrypt(item.from.application.displayName)
      } : null,
      device: item.from.device ? {
        displayName: encrypt(item.from.device.displayName)
      } : null,
      user: item.from.user ? {
        displayName: encrypt(item.from.user.displayName)
      } : null,

    } : null,
    body: item.body ? {
      content: encrypt(item.body.content)
    } : null,
    hasAttachments: item.hasAttachments,
    channelIdentity: item.channelIdentity ? {
      teamId: item.channelIdentity.teamId,
      channelId: encrypt(item.channelIdentity.channelId)
    } : null,
  };
}
/******************************************************* ========== PHASE 6 - END ========== *********************************************************/
/******************************************************* ========== PHASE 7 - START ========== *******************************************************/
export async function processTeamsChannelMessagesReplies(
  logger: Logger,
  client: Client,
  uniqueTeamsChannelMessagesData: ITeamsUniqueChannelMessage[],
): Promise<void> {

  logger.log(`[Impl:processTeamsChannelMessagesReplies] Total uniqueTeamsChannelMessagesData to Process: ${uniqueTeamsChannelMessagesData.length}`);
  // logger.log(`[Impl:processTeamsChannelMessagesReplies] uniqueTeamsChannelMessagesData: ${JSON.stringify(uniqueTeamsChannelMessagesData)}`); // !!!

  const userTeamsChannelBatchRequestsCreated: BatchRequestData[] = uniqueTeamsChannelMessagesData
    .filter(data => data.teamId && data.channelId && data.messageId)
    .flatMap((data) =>
      createTeamsChannelMessagesRepliesRequests(data?.teamId ?? '', data?.channelId ?? '', data?.messageId ?? '').map((request) => ({
        id: request.id,
        method: request.method,
        url: request.url
      }))
    );

  logger.log(`[Impl:processTeamsChannelMessagesReplies] Total GraphAPI - userTeamsChannelBatchRequestsCreated: ${userTeamsChannelBatchRequestsCreated.length} | MAX_GRAPH_API_TEAMS_CHANNEL_MESSAGES_REPLIES_BATCH_COUNTS: ${MAX_GRAPH_API_TEAMS_CHANNEL_MESSAGES_REPLIES_BATCH_COUNTS}`);
  const userSplitTeamsChannelMessagesRepliesBatchRequests = splitArrayIntoChunks(userTeamsChannelBatchRequestsCreated, MAX_GRAPH_API_TEAMS_CHANNEL_MESSAGES_REPLIES_BATCH_COUNTS);
  logger.log(`[Impl:processTeamsChannelMessagesReplies] Total TeamsChannel Split Batch: ${userSplitTeamsChannelMessagesRepliesBatchRequests.length}`);
  // logger.log(`[Impl:processTeamsChannelMessagesReplies] userSplitTeamsChannelMessagesRepliesBatchRequests: ${JSON.stringify(userSplitTeamsChannelMessagesRepliesBatchRequests)}`); // !!!

  // Process all batch requests
  const totalTeamsChannelMessagesBatchRequests = userSplitTeamsChannelMessagesRepliesBatchRequests.length;
  for (let i = 0; i < totalTeamsChannelMessagesBatchRequests; i++) {
    try {
      const currentTeamsChannelMessagesRepliesBatchRequests = i + 1;

      logger.log(`\n==== START - BATCH | TEAMS_CHANNEL_MESSAGES_REPLIES: (Batch Request: ${currentTeamsChannelMessagesRepliesBatchRequests} of ${totalTeamsChannelMessagesBatchRequests}) ====`);
      const currentUserSplitTeamsChannelMessagesRepliesBatchRequests = userSplitTeamsChannelMessagesRepliesBatchRequests[i];
      logger.log(`[Impl:processTeamsChannelMessagesReplies] Processing Batch...`);

      // Process 1 batch request at a time
      const batchResultTeamsChannelMessagesReplies = await fetchJsonBatchForTeamsChannelReplies(logger, client, currentUserSplitTeamsChannelMessagesRepliesBatchRequests);
      // // *** View Raw Response
      // logger.log(`[Impl:processTeamsChannelMessagesReplies] *** batchResultTeamsChannelMessagesReplies == ${JSON.stringify(batchResultTeamsChannelMessagesReplies)} === (Batch Request: ${currentTeamsChannelMessagesRepliesBatchRequests} of ${totalTeamsChannelMessagesBatchRequests})`);  // !!!

      if (!batchResultTeamsChannelMessagesReplies.responses || batchResultTeamsChannelMessagesReplies.responses.length === 0) {
        logger.log(`[Impl:processTeamsChannelMessagesReplies] No Responses in Batch === (Batch Request: ${currentTeamsChannelMessagesRepliesBatchRequests} of ${totalTeamsChannelMessagesBatchRequests})`);
        continue;
      }

      await processBatchResultTeamsChannelMessagesReplies(logger, batchResultTeamsChannelMessagesReplies, currentTeamsChannelMessagesRepliesBatchRequests, totalTeamsChannelMessagesBatchRequests);

      // Clear batch responses AFTER processing ALL users
      batchResultTeamsChannelMessagesReplies.responses = [];
      // Free memory for this batch
      userSplitTeamsChannelMessagesRepliesBatchRequests[i] = [];
      // Add a delay between batches
      await new Promise(resolve => setTimeout(resolve, 3000));

      logger.log(`==== END - BATCH | TEAMS_CHANNEL_MESSAGES_REPLIES: (Batch Request: ${currentTeamsChannelMessagesRepliesBatchRequests} of ${totalTeamsChannelMessagesBatchRequests}) - COMPLETE) ====\n`);
    } catch (error) {
      logger.error(`[Impl:processTeamsChannelMessagesReplies] Error Processing Batch Starting at Index ${i}: ${error}`);
      continue;
    }
    global.gc && global.gc();
  }
}

async function processBatchResultTeamsChannelMessagesReplies(
  logger: Logger,
  batchResultTeamsChannelMessagesReplies: IBatchResponses,
  currentTeamsChannelMessagesRepliesBatchRequests: number,
  totalTeamsChannelMessagesRepliesBatchRequests: number
): Promise<void> {

  const totalBatchResultTeamsChannelMessagesReplies = batchResultTeamsChannelMessagesReplies.responses?.length ?? 0;
  for (let j = 0; j < totalBatchResultTeamsChannelMessagesReplies; j++) {
    const currentBatchResultTeamsChannelMessagesReplies = j + 1;
    const batchResultTeamsChannelMessagesRepliesResponses = (batchResultTeamsChannelMessagesReplies.responses ?? [])[j];
    const teamsChannelId = batchResultTeamsChannelMessagesRepliesResponses.id;

    logger.log(`\n+=+= START - PROCESSING BATCH | TEAMS_CHANNEL_MESSAGES_REPLIES: (teamsChannelId: ${teamsChannelId} / Batch Result: ${currentBatchResultTeamsChannelMessagesReplies} of ${totalBatchResultTeamsChannelMessagesReplies} / Batch Request: ${currentTeamsChannelMessagesRepliesBatchRequests} of ${totalTeamsChannelMessagesRepliesBatchRequests}) +=+=`);

    if (!batchResultTeamsChannelMessagesRepliesResponses || batchResultTeamsChannelMessagesRepliesResponses.status !== 200) {
      const batchResultTeamsChatMessagesResponses_id = batchResultTeamsChannelMessagesRepliesResponses?.id ?? 'teamsChannelId-undefined';
      logger.log(`[Impl:processBatchResultTeamsChannelMessagesReplies] Skipping teamsChannelId ${batchResultTeamsChatMessagesResponses_id} with Error Data: ${batchResultTeamsChannelMessagesRepliesResponses?.status} === (teamsChannelId: ${teamsChannelId} / Batch Result: ${currentBatchResultTeamsChannelMessagesReplies} of ${totalBatchResultTeamsChannelMessagesReplies} / Batch Request: ${currentTeamsChannelMessagesRepliesBatchRequests} of ${totalTeamsChannelMessagesRepliesBatchRequests})`);
      continue;
    }
 
    const singleResultTeamsChannelMessages = { responses: [batchResultTeamsChannelMessagesRepliesResponses] };
    // logger.info(`[Impl:processBatchResultTeamsChannelMessagesReplies] singleResultTeamsChannelMessages == ${JSON.stringify(singleResultTeamsChannelMessages)} === (teamsChannelId: ${teamsChannelId} / Batch Result: ${currentBatchResultTeamsChannelMessagesReplies} of ${totalBatchResultTeamsChannelMessagesReplies} / Batch Request: ${currentTeamsChannelMessagesRepliesBatchRequests} of ${totalTeamsChannelMessagesRepliesBatchRequests})`); // !!!

    const modifiedTeamsChannelMessagesRepliesChunk = processSingleResultTeamsChannelMessagesReplies([singleResultTeamsChannelMessages], logger);
    // // *** View Raw Response
    // logger.info(`[Impl:processBatchResultTeamsChannelMessagesReplies] *** modifiedTeamsChannelMessagesRepliesChunk == ${JSON.stringify(modifiedTeamsChannelMessagesRepliesChunk)} === (teamsChannelId: ${teamsChannelId} / Batch Result: ${currentBatchResultTeamsChannelMessagesReplies} of ${totalBatchResultTeamsChannelMessagesReplies} / Batch Request: ${currentTeamsChannelMessagesRepliesBatchRequests} of ${totalTeamsChannelMessagesRepliesBatchRequests})`); // !!!

    const totalModifiedTeamsChannelMessagesChunk = modifiedTeamsChannelMessagesRepliesChunk.length;
    // Insert each messageChunk
    for (let k = 0; k < totalModifiedTeamsChannelMessagesChunk; k++) {
      const messageChunk = modifiedTeamsChannelMessagesRepliesChunk[k];
      const currentModifiedUserTeamsChatMessagesRepliesChunk = k + 1;
      if (!messageChunk) {
        logger.info(`[Impl:processBatchResultTeamsChannelMessagesReplies] Skipping Undefined TeamsChatMessagesChunk at Index: ${k} === (teamsChannelId: ${teamsChannelId} / Batch Result: ${currentBatchResultTeamsChannelMessagesReplies} of ${totalBatchResultTeamsChannelMessagesReplies} / Batch Request: ${currentTeamsChannelMessagesRepliesBatchRequests} of ${totalTeamsChannelMessagesRepliesBatchRequests})`);
        continue;
      }
      logger.log(`---- START - INSERT COSMOS_DB | TEAMS_CHANNEL_MESSAGES_REPLIES: (Chunk: ${currentModifiedUserTeamsChatMessagesRepliesChunk} of ${totalModifiedTeamsChannelMessagesChunk} with ${messageChunk.body?.value?.length} ChannelMessages Inside) === (teamsChannelId: ${teamsChannelId} / Batch Result: ${currentBatchResultTeamsChannelMessagesReplies} of ${totalBatchResultTeamsChannelMessagesReplies} / Batch Request: ${currentTeamsChannelMessagesRepliesBatchRequests} of ${totalTeamsChannelMessagesRepliesBatchRequests}) ----`);
      try {
        logger.info(`[Impl:processBatchResultTeamsChannelMessagesReplies] Inserting TeamsChannelMessages...`);
        await insertTeamsChannelMessages(logger, [messageChunk]);
        logger.info(`[Impl:processBatchResultTeamsChannelMessagesReplies] Successfully Inserted TeamsChannelMessages...`);
      } catch (error) {
        logger.error(`[Impl:processBatchResultTeamsChannelMessagesReplies] Failed Inserting: ${error} (Chunk: ${currentModifiedUserTeamsChatMessagesRepliesChunk} of ${totalModifiedTeamsChannelMessagesChunk} with ${messageChunk.body?.value?.length} ChannelMessages Inside) === (teamsChannelId: ${teamsChannelId} / Batch Result: ${currentBatchResultTeamsChannelMessagesReplies} of ${totalBatchResultTeamsChannelMessagesReplies} / Batch Request: ${currentTeamsChannelMessagesRepliesBatchRequests} of ${totalTeamsChannelMessagesRepliesBatchRequests})`);
        continue;
      }
      modifiedTeamsChannelMessagesRepliesChunk[k] = {} as IBatchResponseData;
      logger.log(`---- END - INSERT COSMOS_DB | TEAMS_CHANNEL_MESSAGES_REPLIES: (Chunk: ${currentModifiedUserTeamsChatMessagesRepliesChunk} of ${totalModifiedTeamsChannelMessagesChunk} with ${messageChunk.body?.value?.length} ChannelMessages Inside) === (teamsChannelId: ${teamsChannelId} / Batch Result: ${currentBatchResultTeamsChannelMessagesReplies} of ${totalBatchResultTeamsChannelMessagesReplies} / Batch Request: ${currentTeamsChannelMessagesRepliesBatchRequests} of ${totalTeamsChannelMessagesRepliesBatchRequests}) ----`);
    }

    modifiedTeamsChannelMessagesRepliesChunk.length = 0;
    global.gc && global.gc();
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    logger.log(`+=+= END - PROCESSING BATCH | TEAMS_CHANNEL_MESSAGES_REPLIES: (teamsChannelId: ${teamsChannelId} / Batch Result: ${currentBatchResultTeamsChannelMessagesReplies} of ${totalBatchResultTeamsChannelMessagesReplies} / Batch Request: ${currentTeamsChannelMessagesRepliesBatchRequests} of ${totalTeamsChannelMessagesRepliesBatchRequests}) +=+=`);
  }
}

function processSingleResultTeamsChannelMessagesReplies(
  singleResTeamsChannelMessages: IBatchResponses[], 
  logger: Logger
): IBatchResponseData[] {
  const allProcessedData: IBatchResponseData[] = [];

  for (const result of singleResTeamsChannelMessages) {
    if (!result?.responses || !Array.isArray(result.responses)) {
      logger.log(`[Impl:processSingleResultTeamsChannelMessagesReplies] Skipping Invalid TeamsChannelMessagesReplies Result == ${JSON.stringify(result)}`);
      continue;
    }

    // Process each response in the batch
    for (const response of result.responses) {
      const totalTeamsChannelMessagesReplies = response.body?.value?.length || 0;
      logger.log(`[Impl:processSingleResultTeamsChannelMessagesReplies] Total TeamsChannelMessagesReplies in response.body.value: ${totalTeamsChannelMessagesReplies}`);

      const teamsChannelId = response.id ?? '';
      const allTeamsChannelMessagesReplies = response.body?.value as ITeamsChannelMessage[];
      const transformedMessages = allTeamsChannelMessagesReplies.map((item) => {
        const hasAttachments = 
          'attachments' in item && 
          Array.isArray((item as any).attachments) && 
          (item as any).attachments.length > 0;
        return {
          ...item,
          hasAttachments
        };
      });
      //logger.log(`[Impl:processSingleResultTeamsChannelMessagesReplies] transformedMessages: ${JSON.stringify(transformedMessages)}`);  // !!!
      const filteredMessages = transformedMessages.filter((message) => {
        return message.messageType !== 'systemEventMessage' && 
               message.messageType !== 'unknownFutureValue';
      });
      // logger.log(`[Impl:processSingleResultTeamsChannelMessagesReplies] filteredMessages: ${JSON.stringify(filteredMessages)}`);  // !!!
      const chunkSize = MAX_TEAMS_CHANNEL_MESSAGES_REPLIES_CHUNK_SIZE;

      for (let i = 0; i < filteredMessages.length; i += chunkSize) {
        const teamsChannelMessagesRepliesAfterChunk = filteredMessages.slice(i, i + chunkSize);
        // logger.info(`[Impl:processUserTeamsChannelMessagesByChunk] teamsChannelMessagesRepliesAfterChunk: ${JSON.stringify(teamsChannelMessagesRepliesAfterChunk)}`); // !!!
        processTeamsChannelMessagesRepliesChunk(teamsChannelId, teamsChannelMessagesRepliesAfterChunk, allProcessedData, logger);
        teamsChannelMessagesRepliesAfterChunk.length = 0;
      }
      allTeamsChannelMessagesReplies.length = 0;
    }
  }
  // logger.info(`[Impl:processSingleResultTeamsChannelMessagesReplies] allProcessedData == ${JSON.stringify(allProcessedData)}`);
  return allProcessedData;
}

function processTeamsChannelMessagesRepliesChunk(
  teamsChannelId: string,
  teamsChannelMessagesRepliesAfterChunk: ITeamsChannelMessage[],
  allProcessedData: IBatchResponseData[],
  logger: Logger
): void {
  const processedValues: ITeamsChannelMessage[] = [];

  for (const item of teamsChannelMessagesRepliesAfterChunk) {
    if (hasEmptyRequiredFieldsMessagesReplies(item, logger)) {
      continue;
    }
    processedValues.push(createEncryptedMessagesReplies(item));
  }
  if (processedValues.length > 0) {
    allProcessedData.push({
      id: teamsChannelId,
      body: {
        value: processedValues
      }
    } as IBatchResponseData);

    logger.info(`[Impl:processTeamsChannelMessagesRepliesChunk] Successfully Modified TeamsChannelMessagesReplies: ${processedValues.length} TeamsChannelMessagesReplies Inside Chunk | MAX_TEAMS_CHANNEL_MESSAGES_REPLIES_CHUNK_SIZE: ${MAX_TEAMS_CHANNEL_MESSAGES_REPLIES_CHUNK_SIZE}`);
  }
}

function hasEmptyRequiredFieldsMessagesReplies(item: ITeamsChannelMessage, logger: Logger): boolean {
  // Check for empty values
  const isEmpty = (value: any) => value === null || value === undefined || value === "";
  const hasEmptyBody = !item.body || isEmpty(item.body.content);
  const hasEmptyChannelIdentity = !item.channelIdentity || isEmpty(item.channelIdentity.teamId) || isEmpty(item.channelIdentity.channelId);
  const hasEmptyValues = hasEmptyBody || hasEmptyChannelIdentity;
  if (hasEmptyValues) {
    logger.log(`[Impl:hasEmptyRequiredFieldsMessagesReplies] Skipping message with empty required fields. Message ID: ${item.id}`);
    return true;
  }
  return false;
}

function createEncryptedMessagesReplies(item: ITeamsChannelMessage): ITeamsChannelMessage {
  return {
    security_user_id: [],
    id: item.id,
    kind: "Chat",
    replyToId: item.replyToId ? encrypt(item.replyToId) : null,
    messageType: item.messageType ? encrypt(item.messageType) : null,
    createdDateTime: item.createdDateTime ?? null,
    lastModifiedDateTime: item.lastModifiedDateTime ?? null,
    chatId: item.chatId ?? null,
    from: item.from ? {
      application: item.from.application ? {
        displayName: encrypt(item.from.application.displayName)
      } : null,
      device: item.from.device ? {
        displayName: encrypt(item.from.device.displayName)
      } : null,
      user: item.from.user ? {
        displayName: encrypt(item.from.user.displayName)
      } : null,

    } : null,
    body: item.body ? {
      content: encrypt(item.body.content)
    } : null,
    hasAttachments: item.hasAttachments,
    channelIdentity: item.channelIdentity ? {
      teamId: item.channelIdentity.teamId,
      channelId: encrypt(item.channelIdentity.channelId)
    } : null,
  };
}
/******************************************************* ========== PHASE 7 - END ========== *********************************************************/
/******************************************************* ========== PHASE 8 - START ========== *******************************************************/
export async function processTeamsMembers(
  logger: Logger,
  client: Client,
  uniqueTeamsData: Array<{ teamId: string }>,
  targetUsersData: IBatchResponseData[],
): Promise<void> {

  logger.log(`[Impl:processTeamsMembers] Total uniqueTeamsData to Process: ${uniqueTeamsData.length}`);
  // logger.log(`[Impl:processTeamsMembers] uniqueTeamsData: ${JSON.stringify(uniqueTeamsData)}`); // !!!
  // logger.log(`[Impl:processTeamsMembers] targetUsersData: ${JSON.stringify(targetUsersData)}`); // !!!

  const userTeamsMembersBatchRequestsCreated: BatchRequestData[] = uniqueTeamsData
    .filter(data => data.teamId)
    .flatMap((data) =>
      createTeamsMembersRequests(data?.teamId ?? '').map((request) => ({
        id: request.id,
        method: request.method,
        url: request.url
      }))
    );

  logger.log(`[Impl:processTeamsMembers] Total GraphAPI - userTeamsMembersBatchRequestsCreated: ${userTeamsMembersBatchRequestsCreated.length} | MAX_GRAPH_API_TEAMS_CHANNEL_MESSAGES_BATCH_COUNTS: ${MAX_GRAPH_API_TEAMS_CHANNEL_MESSAGES_BATCH_COUNTS}`);
  const userSplitTeamsMembersBatchRequests = splitArrayIntoChunks(userTeamsMembersBatchRequestsCreated, MAX_GRAPH_API_TEAMS_MEMBERS_BATCH_COUNTS);
  logger.log(`[Impl:processTeamsMembers] Total TeamsChannel Split Batch: ${userSplitTeamsMembersBatchRequests.length}`);
  // logger.log(`[Impl:processTeamsMembers] userSplitTeamsMembersBatchRequests: ${JSON.stringify(userSplitTeamsMembersBatchRequests)}`); // !!!

  // Process all batch requests
  const totalTeamsMembersBatchRequests = userSplitTeamsMembersBatchRequests.length;
  for (let i = 0; i < totalTeamsMembersBatchRequests; i++) {
    try {
      const currentTeamsMembersBatchRequests = i + 1;

      logger.log(`\n==== START - BATCH | TEAMS_MEMBERS: (Batch Request: ${currentTeamsMembersBatchRequests} of ${totalTeamsMembersBatchRequests}) ====`);
      const currentUserSplitTeamsMembersBatchRequests = userSplitTeamsMembersBatchRequests[i];
      logger.log(`[Impl:processTeamsMembers] Processing Batch...`);

      // Process 1 batch request at a time
      const batchResultTeamsMembers = await fetchJsonBatchForTeamsChannel(logger, client, currentUserSplitTeamsMembersBatchRequests);
      // // *** View Raw Response
      // logger.log(`[Impl:processTeamsMembers] *** batchResultTeamsMembers == ${JSON.stringify(batchResultTeamsMembers)} === (Batch Request: ${currentTeamsMembersBatchRequests} of ${totalTeamsMembersBatchRequests})`); // !!!

      if (!batchResultTeamsMembers.responses || batchResultTeamsMembers.responses.length === 0) {
        logger.log(`[Impl:processTeamsMembers] No Responses in Batch === (Batch Request: ${currentTeamsMembersBatchRequests} of ${totalTeamsMembersBatchRequests})`);
        continue;
      }

      // Create Filtered version of batchResultTeamsMembers
      const filteredBatchResultTeamsMembers = {
        responses: (batchResultTeamsMembers.responses ?? []).map(response => {
          const filteredResponse = { ...response };
          // Compare the batchResultTeamsMembers "Teams Members" with targetUsersData "Microsotf 365 Members
          if (filteredResponse.body?.value && Array.isArray(filteredResponse.body.value)) {
            const filteredMembers = filteredResponse.body.value.filter(member => 
              targetUsersData.some(user => user.id === (member as { userId: string }).userId)
            );
            const uniqueUserIds = new Set();
            const uniqueMembers = filteredMembers.filter(member => {
              const userId = (member as { userId: string }).userId;
              if (uniqueUserIds.has(userId)) {
                return false;
              } else {
                uniqueUserIds.add(userId);
                return true;
              }
            });
            filteredResponse.body = {
              ...filteredResponse.body,
              value: uniqueMembers
            };
          }
          return filteredResponse;
        })
      };
      // logger.log(`[Impl:processTeamsMembers] *** filtered_batchResultTeamsMembers == ${JSON.stringify(filteredBatchResultTeamsMembers)}`); // !!!

      await processBatchResultTeamsMembers(logger, filteredBatchResultTeamsMembers, currentTeamsMembersBatchRequests, totalTeamsMembersBatchRequests);

      // Clear batch responses AFTER processing ALL users
      batchResultTeamsMembers.responses = [];
      // Free memory for this batch
      userSplitTeamsMembersBatchRequests[i] = [];
      // Add a delay between batches
      await new Promise(resolve => setTimeout(resolve, 3000));

      logger.log(`==== END - BATCH | TEAMS_MEMBERS: (Batch Request: ${currentTeamsMembersBatchRequests} of ${totalTeamsMembersBatchRequests}) ====\n`);
    } catch (error) {
      logger.error(`[Impl:processTeamsMembers] Error Processing Batch Starting at Index ${i}: ${error}`);
      continue;
    }
    global.gc && global.gc();
  }
}

async function processBatchResultTeamsMembers(
  logger: Logger,
  filteredBatchResultTeamsMembers: IBatchResponses,
  currentTeamsMembersBatchRequests: number,
  totalTeamsMembersBatchRequests: number
): Promise<void> {

  const totalBatchResultTeamsMembers = filteredBatchResultTeamsMembers.responses?.length ?? 0;
  for (let j = 0; j < totalBatchResultTeamsMembers; j++) {
    const currentBatchResultTeamsMembers = j + 1;
    const batchResultTeamsMembersResponses = (filteredBatchResultTeamsMembers.responses ?? [])[j];
    const teamId = batchResultTeamsMembersResponses.id;

    logger.log(`\n+=+= START - PROCESSING BATCH | TEAMS_MEMBERS: (teamId: ${teamId} / Batch Result: ${currentBatchResultTeamsMembers} of ${totalBatchResultTeamsMembers} / Batch Request: ${currentTeamsMembersBatchRequests} of ${totalTeamsMembersBatchRequests}) +=+=`);

    if (!batchResultTeamsMembersResponses || batchResultTeamsMembersResponses.status !== 200) {
      logger.log(`[Impl:processBatchResultTeamsMembers] Skipping teamId: ${teamId} with Error Data: ${batchResultTeamsMembersResponses?.status} === (teamId: ${teamId} / Batch Result: ${currentBatchResultTeamsMembers} of ${totalBatchResultTeamsMembers} / Batch Request: ${currentTeamsMembersBatchRequests} of ${totalTeamsMembersBatchRequests})`);
      continue;
    }

    const singleResultTeamsMembers = { responses: [batchResultTeamsMembersResponses] };
    // logger.info(`[Impl:processBatchResultTeamsMembers] singleResultTeamsMembers == ${JSON.stringify(singleResultTeamsMembers)} === (teamId: ${teamId} / Batch Result: ${currentBatchResultTeamsMembers} of ${totalBatchResultTeamsMembers} / Batch Request: ${currentTeamsMembersBatchRequests} of ${totalTeamsMembersBatchRequests})`); // !!!

    const modifiedTeamsMembersChunk = processSingleResultTeamsMembers([singleResultTeamsMembers], logger);
    // // *** View Raw Response
    // logger.info(`[Impl:processBatchResultTeamsMembers] *** modifiedTeamsMembersChunk == ${JSON.stringify(modifiedTeamsMembersChunk)} === (teamId: ${teamId} / Batch Result: ${currentBatchResultTeamsMembers} of ${totalBatchResultTeamsMembers} / Batch Request: ${currentTeamsMembersBatchRequests} of ${totalTeamsMembersBatchRequests})`); // !!!

    const totalModifiedTeamsMembersChunk = modifiedTeamsMembersChunk.length;
    // Insert each membersChunk
    for (let k = 0; k < totalModifiedTeamsMembersChunk; k++) {
      const membersChunk = modifiedTeamsMembersChunk[k];
      const currentModifiedMembersChunk = k + 1;
      if (!membersChunk) {
        logger.info(`[Impl:processBatchResultTeamsMembers] Skipping Undefined TeamsChatMessagesChunk at Index: ${k} === (teamId: ${teamId} / Batch Result: ${currentBatchResultTeamsMembers} of ${totalBatchResultTeamsMembers} / Batch Request: ${currentTeamsMembersBatchRequests} of ${totalTeamsMembersBatchRequests})`);
        continue;
      }
      logger.log(`---- START - UPDATE COSMOS_DB | TEAM_MEMBERS: (Chunk: ${currentModifiedMembersChunk} of ${totalModifiedTeamsMembersChunk} with ${membersChunk.body?.value?.length} TeamsMembers Inside) === (teamId: ${teamId} / Batch Result: ${currentBatchResultTeamsMembers} of ${totalBatchResultTeamsMembers} / Batch Request: ${currentTeamsMembersBatchRequests} of ${totalTeamsMembersBatchRequests}) ----`);
      try {
        logger.info(`[Impl:processBatchResultTeamsMembers] Updating TeamsMembers...`);
        await updateTeamsMembers(logger, [membersChunk]);
        logger.info(`[Impl:processBatchResultTeamsMembers] Successfully Updated TeamsMembers...`);

      } catch (error) {
        logger.error(`[Impl:processBatchResultTeamsMembers] Failed Inserting: ${error} (teamId: ${teamId} / Batch Result: ${currentBatchResultTeamsMembers} of ${totalBatchResultTeamsMembers} / Batch Request: ${currentTeamsMembersBatchRequests} of ${totalTeamsMembersBatchRequests})`);
        continue;
      }
      modifiedTeamsMembersChunk[k] = {} as IBatchResponseData;
      logger.log(`---- END - UPDATE COSMOS_DB | TEAM_MEMBERS: (Chunk: ${currentModifiedMembersChunk} of ${totalModifiedTeamsMembersChunk} with ${membersChunk.body?.value?.length} TeamsMembers Inside) === (teamId: ${teamId} / Batch Result: ${currentBatchResultTeamsMembers} of ${totalBatchResultTeamsMembers} / Batch Request: ${currentTeamsMembersBatchRequests} of ${totalTeamsMembersBatchRequests}) ----`);
   }

    modifiedTeamsMembersChunk.length = 0;
    global.gc && global.gc();
    await new Promise(resolve => setTimeout(resolve, 3000));

    logger.log(`+=+= END - PROCESSING BATCH | TEAMS_MEMBERS: (teamId: ${teamId} / Batch Result: ${currentBatchResultTeamsMembers} of ${totalBatchResultTeamsMembers} / Batch Request: ${currentTeamsMembersBatchRequests} of ${totalTeamsMembersBatchRequests}) +=+=`);
  }
}

function processSingleResultTeamsMembers(
  singleResultTeamsMembers: IBatchResponses[], 
  logger: Logger
): IBatchResponseData[] {
  const allProcessedData: IBatchResponseData[] = [];

  for (const result of singleResultTeamsMembers) {
    if (!result?.responses || !Array.isArray(result.responses)) {
      logger.log(`[Impl:processSingleResultTeamsMembers] Skipping Invalid TeamsMembers Result == ${JSON.stringify(result)}`);
      continue;
    }

    // Process each response in the batch
    for (const response of result.responses) {
      const totalTeamsChannelMessages = response.body?.value?.length || 0;
      logger.log(`[Impl:processSingleResultTeamsMembers] Total TeamsMembers in response.body.value: ${totalTeamsChannelMessages}`);

      const teamsId = response.id ?? '';
      const allTeamsMembers = response.body?.value as ITeamsMembers[];
      // logger.log(`[Impl:processSingleResultTeamsMembers] allTeamsMembers: ${JSON.stringify(allTeamsMembers)}`); // !!!
      const chunkSize = MAX_TEAMS_MEMBERS_CHUNK_SIZE;

      for (let i = 0; i < allTeamsMembers.length; i += chunkSize) {
        const teamsMembersAfterChunk = allTeamsMembers.slice(i, i + chunkSize);
        // logger.info(`[Impl:processUserTeamsMembersByChunk] teamsMembersAfterChunk: ${JSON.stringify(teamsMembersAfterChunk)}`); // !!!
        processTeamsMembersChunk(teamsId, teamsMembersAfterChunk, allProcessedData, logger);
        teamsMembersAfterChunk.length = 0;
      }
      allTeamsMembers.length = 0;
    }
  }
  // logger.info(`[Impl:processSingleResultTeamsMembers] allProcessedData == ${JSON.stringify(allProcessedData)}`); // !!!
  return allProcessedData;
}

function processTeamsMembersChunk(
  teamsId: string,
  teamsMembersAfterChunk: ITeamsMembers[],
  allProcessedData: IBatchResponseData[],
  logger: Logger
): void {
  const processedValues: ITeamsMembers[] = [];

  for (const item of teamsMembersAfterChunk) {
    if (hasEmptyRequiredFieldsMembers(item, logger)) {
      continue;
    }
    processedValues.push(createFieldMembers(item));
  }
  if (processedValues.length > 0) {
    allProcessedData.push({
      id: teamsId,
      body: {
        value: processedValues
      }
    } as IBatchResponseData);
    logger.info(`[Impl:processTeamsMembersChunk] Successfully Modified TeamsMembers: ${processedValues.length} TeamsMembers Inside Chunk | MAX_TEAMS_MEMBERS_CHUNK_SIZE: ${MAX_TEAMS_MEMBERS_CHUNK_SIZE}`);
  }
}

function hasEmptyRequiredFieldsMembers(item: ITeamsMembers, logger: Logger): boolean {
  // Check for empty values
  const isEmpty = (value: any) => value === null || value === undefined || value === "";
  const hasEmptyId = !item.id || isEmpty(item.id);
  const hasEmptyValues = hasEmptyId;
  if (hasEmptyValues) {
    logger.log(`[Impl:hasEmptyRequiredFieldsMembers] Skipping message with empty required fields. Message ID: ${item.id}`);
    return true;
  }
  return false;
}

function createFieldMembers(item: ITeamsMembers): ITeamsMembers {
  return {
    id: item.id,
    displayName: item.displayName,
    visibleHistoryStartDateTime: item.visibleHistoryStartDateTime,
    userId: item.userId,
    email: item.email
  };
}
/******************************************************* ========== PHASE 8 - END ========== *******************************************************/