{"name": "chat-upload<PERSON>t<PERSON>b", "version": "1.0.0", "main": "index.js", "scripts": {"start": "ts-node ./src/index.ts", "start-dist": "ts-node ./dist/src/index.js", "build": "tsc", "setup1": "ts-node ./scripts/1-build_and_compress.ts", "setup2": "ts-node ./scripts/2-upload_blob_compress.ts", "setup3": "ts-node ./scripts/3-setup_batch.ts"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@azure/batch": "^12.0.0", "@azure/cosmos": "^4.2.0", "@azure/identity": "^4.7.0", "@azure/storage-blob": "^12.26.0", "@microsoft/microsoft-graph-client": "^3.0.7", "@microsoft/microsoft-graph-types": "^2.40.0", "crypto-js": "^4.2.0", "dotenv": "^16.4.7", "isomorphic-fetch": "^3.0.0", "ts-node": "^10.9.2", "typescript": "^5.8.2"}, "description": "", "devDependencies": {"@types/crypto-js": "4.2.0"}}