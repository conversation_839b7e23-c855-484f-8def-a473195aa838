import { Con<PERSON><PERSON>, CosmosClient } from "@azure/cosmos";
import { IBatchResponseData, ITeamsUniqueChatsMessages } from "./models";
import { CustomLogger } from "./log";
import * as dotenv from "dotenv";

dotenv.config();

const cosmosDBEndpoint = process.env["COSMOS_DB_ENDPOINT"];
const cosmosDBKey = process.env["COSMOS_DB_KEY"];
const databaseName = process.env["COSMOS_DB_DATABASE"];
const containerName = process.env["COSMOS_DB_CONTAINER"];

let cosmosClient: CosmosClient | null = null;

function getClient(logger: CustomLogger) {
  try {
    if (!cosmosDBEndpoint || !cosmosDBKey) {
      throw new Error("[CosmosDB:getClient] cosmosDBEndpoint and cosmosDBKey must be defined");
    }
    if (!cosmosClient) {
      logger.log("[CosmosDB:getClient] Initializing Client Connection...");
      cosmosClient = new CosmosClient({
        endpoint: cosmosDBEndpoint,
        key: cosmosDBKey,
      });
      logger.log("[CosmosDB:getClient] Client Initialized Successfully");
    } else {
      logger.log("[CosmosDB:getClient] Reusing Existing Connection");
    }

    return cosmosClient;
  } catch (error) {
    logger.log(`[CosmosDB:getClient] Error Initialization: ${error}`);
    throw error;
  }
}

export async function validateCosmosDBConnection(
  logger: CustomLogger
): Promise<void> {
  try {
    if (!databaseName || !containerName) {
      throw new Error("[CosmosDB:validateCosmosDBConnection] databaseName and containerName must be defined");
    }
    const client = getClient(logger);

    // Test General Connection
    logger.log(`[CosmosDB:validateCosmosDBConnection] Testing Cosmos DB Connection...`);
    const { resources: databases } = await client.databases
      .readAll()
      .fetchAll();
    logger.log(`[CosmosDB:validateCosmosDBConnection] Successfully Connected! Found ${databases.length} Databases`);

    // Test Database Connection
    const database = client.database(databaseName);
    const dbResponse = await database.read();
    if (dbResponse.resource) {
      logger.log(`[CosmosDB:validateCosmosDBConnection] Connected to Database: ${dbResponse.resource.id}`);
    } else {
      throw new Error("[CosmosDB:validateCosmosDBConnection] Database resource is undefined");
    }

    // Test Container Connections
    const container = database.container(containerName);
    const containerResponse = await container.read();
    if (containerResponse.resource) {
      logger.log(`[CosmosDB:validateCosmosDBConnection] Connected to Container: ${containerResponse.resource.id}`);
    } else {
      throw new Error("[CosmosDB:validateCosmosDBConnection] Container resource is undefined");
    }

    // Count Container Details
    const {
      resources: [count],
    } = await container.items.query("SELECT VALUE COUNT(1) FROM c").fetchAll();
    logger.log(`[CosmosDB:validateCosmosDBConnection] Container has: ${count} Items`);
  } catch (error) {
    logger.log(`[CosmosDB:validateCosmosDBConnection] Error Connection: ${error}`);
    throw error;
  }
}

export async function insertUniqueChatsMessages(
  logger: CustomLogger,
  dataUniqueChatsMessages: IBatchResponseData[]
): Promise<void> {

  // logger.log(`[CosmosDB:insertUniqueChatsMessages] dataUniqueChatsMessages: ${JSON.stringify(dataUniqueChatsMessages)}`); // !!!

  try {
    if (!databaseName || !containerName) {
      throw new Error("[CosmosDB:insertUniqueChatsMessages] databaseName and containerName must be defined");
    }

    const client = getClient(logger);
    const container = client.database(databaseName).container(containerName);

    const modifiedDataUniqueChatsMessages: ITeamsUniqueChatsMessages[][] = dataUniqueChatsMessages
      .map((message) => message.body?.value || [])
      .filter(Array.isArray);

    const insertedCount = await processUniqueChatsMessages(container, modifiedDataUniqueChatsMessages, logger);

    logger.log(`[CosmosDB:insertUniqueChatsMessages] Inserted: ${insertedCount} New TeamsChatMessages to Cosmos DB`);
    const totalMessageCount = modifiedDataUniqueChatsMessages.reduce((count, array) => count + (array ? array.length : 0),0 );
    logger.log(`[CosmosDB:insertUniqueChatsMessages] Skipped: ${totalMessageCount - insertedCount} Existing TeamsChatMessages`);
  } catch (error) {
    logger.log(`[CosmosDB:insertUniqueChatsMessages] Error Processing Messages: ${error}`);
    throw error;
  }
}

async function processUniqueChatsMessages(
  container: Container,
  modifiedDataUniqueChatsMessages: ITeamsUniqueChatsMessages[][],
  logger: CustomLogger
): Promise<number> {
  let insertedCount = 0;

  logger.log(`[CosmosDB:processUniqueChatsMessages] Total modifiedDataUniqueChatsMessages: ${modifiedDataUniqueChatsMessages.length}`);
  // logger.log(`[CosmosDB:processUniqueChatsMessages] modifiedDataUniqueChatsMessages: ${JSON.stringify(modifiedDataUniqueChatsMessages)}`); // !!!

  for (const messageArray of modifiedDataUniqueChatsMessages) {
    for (const message of messageArray) {
      if (!message.id) {
        logger.log(`[CosmosDB:Process] Error: message.id is undefined for message: ${JSON.stringify(message.id)}`);
        continue;
      }
      const querySpec = {
        query: "SELECT * FROM c WHERE c.id = @id",
        parameters: [{ name: "@id", value: message.id }]
      };
      try {
        const { resources: existingMessages } = await container.items.query(querySpec).fetchAll();
        if (existingMessages.length === 0) {
          await container.items.create(message);
          insertedCount++;
        }
      } catch (error) {
        logger.log(`[CosmosDB:processUniqueChatsMessages] Error processing message: ${error}`);
      }
    }
  }
  return insertedCount;
}

export async function updateChatsMembers(
  logger: CustomLogger,
  dataChatMembers: IBatchResponseData[]
): Promise<void> {

  // logger.log(`[CosmosDB:updateChatsMembers] dataChatMembers: ${JSON.stringify(dataChatMembers)}`); // !!!

  try {
    if (!databaseName || !containerName) {
      throw new Error("[CosmosDB:updateChatsMembers] databaseName and containerName must be defined");
    }
    const client = getClient(logger);
    const container = client.database(databaseName).container(containerName);

    const modifiedDataChatsMembers = dataChatMembers
    .map((chat) => ({
      chatId: chat.id,
      members: chat.body?.value || []
    }))
  
    const updatedMessagesCount = await processChatMembers(container, modifiedDataChatsMembers, logger);
    logger.log(`[CosmosDB:updateChatsMembers] Updated ${updatedMessagesCount} Messages to Cosmos DB`);
  } catch (error) {
    logger.log(`[CosmosDB:updateChatsMembers] Error processing system messages: ${error}`);
    throw error;
  }
}

async function processChatMembers(
  container: Container,
  modifiedDataChatsMembers: any[],
  logger: CustomLogger
): Promise<number> {
  let updatedCount = 0;
  
  logger.log(`[CosmosDB:processChatMembers] Total modifiedDataChatsMembers: ${modifiedDataChatsMembers.length}`);
  
  for (const chatEntry of modifiedDataChatsMembers) {
    const chatId = chatEntry.chatId;
    const members = chatEntry.members;
    
    if (!members || !Array.isArray(members)) {
      logger.log(`[CosmosDB:processChatMembers] Skipping Invalid Members for chat: ${chatId}`);
      continue;
    }
    
    const querySpec = {
      query: "SELECT * FROM c WHERE c.chatId = @chatId",
      parameters: [
        {
          name: "@chatId",
          value: chatId
        }
      ]
    };
    
    const { resources: messages } = await container.items.query(querySpec).fetchAll();
    logger.log(`[CosmosDB:processChatMembers] Found ${messages.length} Messages for Chat: ${chatId}`);
    
    for (const message of messages) {
      // Get the message lastModifiedDateTime
      const messageLastModifiedDatetime = message.lastModifiedDateTime;

      if (!messageLastModifiedDatetime) {
        logger.log(`[CosmosDB:processChatMembers] Missing lastModifiedDateTime for message: ${message.id}`);
        continue;
      }
      // Validate lastModifiedDateTime
      const messageDate = new Date(messageLastModifiedDatetime);
      if (isNaN(messageDate.getTime())) {
        logger.log(`[CosmosDB:processChatMembers] Invalid lastModifiedDateTime for message: ${message.id} - ${messageLastModifiedDatetime}`);
        continue;
      }
      logger.log(`[CosmosDB:processChatMembers] messageLastModifiedDatetime: ${messageLastModifiedDatetime}`);

      // Filter eligible users based on message lastModifiedDateTime
      const eligibleUserIds = members
        .filter(member => {
          if (!member.userId) return false;
          
          const memberJoinDate = new Date(member.visibleHistoryStartDateTime || "0001-01-01T00:00:00Z");
          if (isNaN(memberJoinDate.getTime())) return false;
          
          return messageDate >= memberJoinDate;
        })
        .map(member => member.userId);

        // Check if security actually changed
        const currentSecuritySorted = (message.security_user_id || []).slice().sort((a: string, b: string) => a.localeCompare(b));
        const newSecuritySorted = eligibleUserIds.slice().sort((a: string, b: string) => a.localeCompare(b));

        const currentSecurity = JSON.stringify(currentSecuritySorted);
        const newSecurity = JSON.stringify(newSecuritySorted);

        if (currentSecurity !== newSecurity) {
          message.security_user_id = eligibleUserIds;
          
          try {
            await container.items.upsert(message);
            updatedCount++;
          } catch (error) {
            logger.log(`[CosmosDB:processChatMembers] Error Updating Message: ${message.id}: ${error}`);
          }
        } else {
          logger.log(`[CosmosDB:processChatMembers] No change needed for message: ${message.id}`);
        }
    }
  }
  
  return updatedCount;
}